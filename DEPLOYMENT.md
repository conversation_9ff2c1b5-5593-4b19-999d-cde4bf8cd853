# QuickCV Deployment Guide for Coolify

This guide covers deploying QuickCV to production using Coolify.

## Prerequisites

1. **Coolify instance** running on your server
2. **Domain name** pointed to your Coolify server
3. **Clerk account** for authentication
4. **UploadThing account** for file uploads

## Environment Variables

Set these environment variables in your Coolify deployment:

### Required Variables

```bash
# Database
DATABASE_URL="file:/app/data/production.db"

# Authentication - Clerk
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_key_here
CLERK_SECRET_KEY=sk_test_your_secret_here
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/

# File Upload - UploadThing
UPLOADTHING_SECRET=sk_live_your_secret_here
UPLOADTHING_APP_ID=your_app_id_here

# Application
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NODE_ENV=production
```

## Deployment Steps

### 1. Create New Project in Coolify

1. Go to your Coolify dashboard
2. Click "New Project"
3. Choose "Docker Compose" or "Dockerfile"
4. Connect your Git repository

### 2. Configure Build Settings

- **Build Command**: `npm run build`
- **Port**: `3000`
- **Health Check Path**: `/api/health`

### 3. Set Environment Variables

Copy all the environment variables from the list above into your Coolify project settings.

### 4. Configure Persistent Storage

Create a volume for the SQLite database:
- **Source**: `app_data`
- **Destination**: `/app/data`

### 5. Deploy

Click "Deploy" in your Coolify dashboard.

## Database Setup

The application will automatically:
1. Generate Drizzle migrations
2. Run database migrations
3. Seed with resume templates

## Health Checks

The application includes a health check endpoint at `/api/health` that Coolify can use to monitor the deployment.

## File Structure

```
/app/
├── data/              # SQLite database (persistent volume)
├── public/            # Static assets
├── .next/             # Next.js build output
├── db/               # Drizzle schema and migrations
└── drizzle/          # Generated migration files
```

## Troubleshooting

### Common Issues

1. **Database Connection**: Ensure the persistent volume is mounted correctly
2. **Clerk Authentication**: Verify all Clerk environment variables are set
3. **UploadThing**: Check file upload keys and app ID
4. **Build Failures**: Check build logs for missing dependencies

### Logs

Check application logs in Coolify dashboard:
- Build logs for compilation issues
- Runtime logs for application errors
- Health check logs for deployment status

## Post-Deployment

1. **Test Authentication**: Try signing up/in
2. **Test File Upload**: Upload a profile photo
3. **Test Resume Creation**: Create and preview a resume
4. **Test PDF Export**: Generate PDF from resume

## Backup Strategy

### Database Backup

```bash
# Backup SQLite database
docker exec -t coolify-app-container sqlite3 /app/data/production.db ".backup /app/data/backup.db"

# Copy backup to host
docker cp coolify-app-container:/app/data/backup.db ./backup.db
```

### Restore Database

```bash
# Copy backup to container
docker cp ./backup.db coolify-app-container:/app/data/restore.db

# Restore database
docker exec -t coolify-app-container sqlite3 /app/data/production.db ".restore /app/data/restore.db"
```

## Monitoring

The application exposes metrics at:
- Health check: `/api/health`
- Application logs via Coolify dashboard

## Updates

To update the application:
1. Push changes to your Git repository
2. Trigger a new deployment in Coolify
3. Monitor the health check endpoint

## Security

- All secrets are stored as environment variables
- Database is isolated in a persistent volume
- Application runs as non-root user
- Health checks monitor application status