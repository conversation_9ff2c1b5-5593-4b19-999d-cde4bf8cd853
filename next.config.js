const createNextIntlPlugin = require("next-intl/plugin");

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {
	// Performance optimizations
	compress: true,
	poweredByHeader: false,
	images: {
		formats: ["image/avif", "image/webp"],
		minimumCacheTTL: 60,
		remotePatterns: [
			{
				protocol: "https",
				hostname: "ui-avatars.com",
			},
			{
				protocol: "https",
				hostname: "utfs.io",
			},
			{
				protocol: "https",
				hostname: "images.unsplash.com",
			},
			{
				protocol: "https",
				hostname: "randomuser.me",
			},
		],
	},
	experimental: {
		serverActions: {
			bodySizeLimit: "4mb",
		},
	},
	// SWC compiler optimizations
	compiler: {
		removeConsole: process.env.NODE_ENV === "production",
	},
	// Help with build manifest stability (moved from experimental.turbo)
	turbopack: {
		rules: {
			"*.svg": {
				loaders: ["@svgr/webpack"],
				as: "*.js",
			},
		},
	},
	// Improve build stability and caching
	webpack: (config, { dev, isServer }) => {
		if (dev && !isServer) {
			// Prevent build manifest race conditions in development
			config.watchOptions = {
				...config.watchOptions,
				ignored: ["**/node_modules", "**/.next"],
			};
		}

		// Enable build caching
		if (!dev) {
			config.cache = {
				type: "filesystem",
				buildDependencies: {
					config: [__filename],
				},
			};
		}

		return config;
	},
};

module.exports = withNextIntl(nextConfig);
