import { heroui } from "@heroui/theme";

/** @type {import('tailwindcss').Config} */
const config = {
	content: [
		"./components/**/*.{js,ts,jsx,tsx,mdx}",
		"./app/**/*.{js,ts,jsx,tsx,mdx}",
		"./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
	],
	theme: {
		extend: {
			fontFamily: {
				sans: ["var(--font-sans)"],
				arabic: ["var(--font-arabic)", "'Noto Sans Arabic'", "sans-serif"],
			},
			animation: {
				"fade-in": "fadeIn 0.5s ease-in-out",
				"slide-up": "slideUp 0.3s ease-out",
				"scale-in": "scaleIn 0.2s ease-out",
				"bounce-subtle": "bounceSubtle 0.6s ease-out",
			},
			keyframes: {
				fadeIn: {
					"0%": { opacity: "0" },
					"100%": { opacity: "1" },
				},
				slideUp: {
					"0%": { transform: "translateY(10px)", opacity: "0" },
					"100%": { transform: "translateY(0)", opacity: "1" },
				},
				scaleIn: {
					"0%": { transform: "scale(0.95)", opacity: "0" },
					"100%": { transform: "scale(1)", opacity: "1" },
				},
				bounceSubtle: {
					"0%, 20%, 50%, 80%, 100%": { transform: "translateY(0)" },
					"40%": { transform: "translateY(-4px)" },
					"60%": { transform: "translateY(-2px)" },
				},
			},
		},
	},
	darkMode: "class",
	plugins: [
		heroui(),
		require("@tailwindcss/typography"),
		require("tailwindcss-rtl"),
	],
};

module.exports = config;
