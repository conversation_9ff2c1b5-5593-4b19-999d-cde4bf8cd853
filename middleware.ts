import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import createMiddleware from "next-intl/middleware";
import { routing } from "./i18n/routing";

const intlMiddleware = createMiddleware(routing);

const isPublicRoute = createRouteMatcher([
	"/(ar|en)/",
	"/(ar|en)/about",
	"/(ar|en)/pricing",
	"/(ar|en)/docs",
	"/(ar|en)/blog",
	"/(ar|en)/templates",
	"/(ar|en)/templates/(.*)",
	"/(ar|en)/pdf/(.*)",
	"/pdf/(.*)",
	"/(ar|en)/sign-in(.*)",
	"/(ar|en)/sign-up(.*)",
	"/api/uploadthing(.*)",
]);

export default clerkMiddleware((auth, req) => {
	// Skip internationalization for API routes and PDF routes
	if (
		req.nextUrl.pathname.startsWith("/api/") ||
		req.nextUrl.pathname.startsWith("/pdf/")
	) {
		// Only apply auth protection for non-public routes
		if (!isPublicRoute(req)) {
			auth.protect();
		}
		return;
	}

	// Apply internationalization middleware for other routes
	const intlResponse = intlMiddleware(req);

	// Protect private routes
	if (!isPublicRoute(req)) {
		auth.protect();
	}

	return intlResponse;
});

export const config = {
	matcher: [
		// Skip Next.js internals and all static files, unless found in search params
		"/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
		// Always run for API routes
		"/(api|trpc)(.*)",
	],
};
