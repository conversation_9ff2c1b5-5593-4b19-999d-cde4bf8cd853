{"name": "quickcv", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start -p ${PORT:-3000}", "lint": "biome check --write .", "lint:check": "biome check .", "format": "biome format --write .", "format:check": "biome format .", "clean": "./scripts/clean-dev.sh", "dev:clean": "./scripts/clean-dev.sh && bun run dev", "db:local": "turso dev --db-file local-db.db", "db:generate": "drizzle-kit generate", "db:migrate": "bun run db/migrate.ts", "db:studio": "drizzle-kit studio"}, "dependencies": {"@bprogress/next": "^3.2.12", "@clerk/nextjs": "^6.25.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@heroui/react": "^2.8.1", "@hugocxl/react-to-image": "^0.0.9", "@internationalized/date": "^3.8.2", "@libsql/client": "^0.15.10", "@paralleldrive/cuid2": "^2.2.2", "@react-aria/ssr": "3.9.8", "@react-aria/visually-hidden": "3.8.23", "@tiptap/extension-color": "^2.26.1", "@tiptap/extension-placeholder": "^2.26.1", "@tiptap/pm": "^2.26.1", "@tiptap/react": "^2.26.1", "@tiptap/starter-kit": "^2.26.1", "@types/pg": "^8.15.4", "@uploadthing/react": "^7.3.2", "clsx": "2.1.1", "dompurify": "^3.2.6", "drizzle-orm": "^0.44.3", "framer-motion": "11.13.1", "intl-messageformat": "10.7.16", "next": "15.3.1", "next-intl": "^4.3.4", "next-themes": "0.4.6", "pg": "^8.16.3", "puppeteer": "^23.11.1", "react": "18.3.1", "react-autosave": "^0.5.0", "react-dom": "18.3.1", "react-hot-toast": "^2.5.2", "react-to-print": "^3.1.1", "recharts": "^2.15.4", "styled-jsx": "^5.1.7", "tailwindcss-rtl": "^0.9.0", "uploadthing": "^7.7.3", "usehooks-ts": "^3.1.1", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@iconify/react": "^6.0.0", "@react-types/shared": "3.29.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "22.15.3", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "autoprefixer": "10.4.21", "drizzle-kit": "^0.31.4", "postcss": "8.5.3", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16", "tsx": "^4.20.3", "typescript": "5.6.3"}}