# Vercel Environment Variables

Add these environment variables in your Vercel dashboard (Project Settings > Environment Variables):

## Database (Turso)
```
TURSO_DATABASE_URL=libsql://your-database.turso.io
TURSO_AUTH_TOKEN=your-auth-token-here
```

## Authentication (<PERSON>)
```
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_key_here
CLERK_SECRET_KEY=sk_test_your_secret_here
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/
```

## File Upload (UploadThing)
```
UPLOADTHING_SECRET=sk_live_your_secret_here
UPLOADTHING_APP_ID=your_app_id_here
```

## Optional
```
NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
```

## Build Notes
- Vercel is configured to use Bun for faster builds
- Database migrations are generated automatically during build
- Function timeout increased to 30s for PDF generation