import { type ClassValue, clsx } from "clsx";
import DOMPurify from "dompurify";

export const createEmptyResume = (title: string) => {
	return {
		title: title || "",
		firstName: "",
		lastName: "",
		jobTitle: "",
		address: "",
		email: "",
		website: "",
		bio: "",
		birthDate: null,
		city: "",
		street: "",
		country: "",
		showPhoto: false,
		photo: null,
	};
};

export const getInitials = (firstName?: string, lastName?: string) => {
	const first = firstName?.charAt(0) || "";
	const last = lastName?.charAt(0) || "";
	return (first + last).toUpperCase();
};

export const formatDate = (dateString: string) => {
	const date = new Date(dateString);
	return new Intl.DateTimeFormat("en-US", {
		day: "numeric",
		month: "short",
		year: "numeric",
	}).format(date);
};

export const upperCaseFirstLetter = (str: string) => {
	return str.charAt(0).toUpperCase() + str.slice(1);
};

export const createMarkup = (htmlContent: string): { __html: string } => {
	return {
		__html:
			typeof DOMPurify.sanitize === "function"
				? DOMPurify.sanitize(htmlContent)
				: htmlContent,
	};
};

export const isUrl = (string: string | null | undefined) => {
	if (!string) return false;

	const urlRegex = /https?:\/\/[^\n ]+/i;

	return urlRegex.test(string);
};

export const isEmptyString = (string: string) => {
	if (string === "<p></p>") return true;
	return string.trim().length === 0;
};

export function cn(...inputs: ClassValue[]) {
	return clsx(inputs);
}
