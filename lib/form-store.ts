import { create } from "zustand";
import { subscribeWithSelector } from "zustand/middleware";

interface FormStore {
	formData: Record<string, any>;
	setFormData: (data: Record<string, any>) => void;
	updateField: (fieldName: string, value: any) => void;
	handleInputChange: (
		e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
	) => void;
	resetForm: () => void;
}

export const useFormStore = create<FormStore>()(
	subscribeWithSelector((set, get) => ({
		formData: {},

		setFormData: (data) => set({ formData: data }),

		updateField: (fieldName, value) =>
			set((state) => ({
				formData: { ...state.formData, [fieldName]: value },
			})),

		handleInputChange: (e) => {
			const { name, value, type } = e.target;
			const checked = (e.target as HTMLInputElement).checked;
			const newValue = type === "checkbox" ? checked : value;

			// Extract field name from complex nested name patterns
			const fieldName = name.split("][").pop()?.replace("]", "") || name;

			get().updateField(fieldName, newValue);
		},

		resetForm: () => set({ formData: {} }),
	})),
);
