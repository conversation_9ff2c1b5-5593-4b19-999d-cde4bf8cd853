// Application constants
export const APP_NAME = "QuickCV";
export const DEFAULT_LOCALE = "ar";
export const SUPPORTED_LOCALES = ["ar", "en"] as const;

export const features = [
	{
		icon: "🎨",
		key: "templates",
		titleKey: "features.templates.title",
		descriptionKey: "features.templates.description",
	},
	{
		icon: "🚀",
		key: "ats",
		titleKey: "features.ats.title",
		descriptionKey: "features.ats.description",
	},
	{
		icon: "⚡",
		key: "instant",
		titleKey: "features.instant.title",
		descriptionKey: "features.instant.description",
	},
	{
		icon: "🌍",
		key: "multilingual",
		titleKey: "features.multilingual.title",
		descriptionKey: "features.multilingual.description",
	},
	{
		icon: "💾",
		key: "autosave",
		titleKey: "features.autosave.title",
		descriptionKey: "features.autosave.description",
	},
	{
		icon: "📱",
		key: "responsive",
		titleKey: "features.responsive.title",
		descriptionKey: "features.responsive.description",
	},
	{
		icon: "🌐",
		key: "website",
		titleKey: "features.website.title",
		descriptionKey: "features.website.description",
	},
];

export const featuredTemplates = [
	{
		id: "azurill",
		name: "Azurill",
		description: "Clean and modern design perfect for tech professionals",
		image: "/templates/azurill-preview.jpg",
		atsOptimized: true,
		category: "Modern",
	},
	{
		id: "chikorita",
		name: "Chikorita",
		description: "Professional layout ideal for corporate environments",
		image: "/templates/chikorita-preview.jpg",
		atsOptimized: true,
		category: "Professional",
	},
	{
		id: "bronzor",
		name: "Bronzor",
		description: "Minimalist design that highlights your experience",
		image: "/templates/bronzor-preview.jpg",
		atsOptimized: true,
		category: "Minimalist",
	},
];

export const testimonials = [
	{
		name: "Sarah Johnson",
		role: "Software Engineer",
		company: "Google",
		content:
			"QuickCV helped me land my dream job! The ATS optimization feature is a game-changer.",
		rating: 5,
	},
	{
		name: "Ahmed Hassan",
		role: "Product Manager",
		company: "Microsoft",
		content:
			"The multilingual support and RTL layout made it perfect for my Arabic resume.",
		rating: 5,
	},
	{
		name: "Maria Rodriguez",
		role: "UX Designer",
		company: "Apple",
		content: "Beautiful templates and instant editing saved me hours of work.",
		rating: 5,
	},
];

export const staticWebsiteTemplates = [
	{
		name: "Rocket",
		slug: "rocket",
		preview: "/assets/images/websites/rocket.webp",
	},
	{
		name: "Modern",
		slug: "modern",
		preview: "/assets/images/websites/modern.webp",
	},
	{
		name: "Elegant",
		slug: "elegant",
		preview: "/assets/images/websites/elegant.webp",
	},
];

export const staticResumeTemplates = [
	{
		name: "Azurill",
		slug: "azurill",
		atsScore: "85",
		category: "modern",
		features: "Clean, professional layout",
		description: "A modern, clean template with excellent readability",
	},
	{
		name: "Bronzor",
		slug: "bronzor",
		atsScore: "90",
		category: "classic",
		features: "Traditional format",
		description: "Classic professional resume template",
	},
	{
		name: "Chikorita",
		slug: "chikorita",
		atsScore: "88",
		category: "modern",
		features: "Colorful sections",
		description: "Modern template with colorful section headers",
	},
	{
		name: "Ditto",
		slug: "ditto",
		atsScore: "92",
		category: "minimalist",
		features: "Simple layout",
		description: "Minimalist design focusing on content",
	},
	{
		name: "Gengar",
		slug: "gengar",
		atsScore: "87",
		category: "creative",
		features: "Creative layout",
		description: "Creative template with unique styling",
	},
	{
		name: "Glalie",
		slug: "glalie",
		atsScore: "89",
		category: "professional",
		features: "Corporate style",
		description: "Professional corporate template",
	},
	{
		name: "Kakuna",
		slug: "kakuna",
		atsScore: "91",
		category: "modern",
		features: "Structured sections",
		description: "Well-structured modern template",
	},
	{
		name: "Leafish",
		slug: "leafish",
		atsScore: "86",
		category: "creative",
		features: "Nature-inspired",
		description: "Creative template with nature-inspired design",
	},
	{
		name: "Nosepass",
		slug: "nosepass",
		atsScore: "88",
		category: "classic",
		features: "Traditional format",
		description: "Classic template with traditional formatting",
	},
	{
		name: "Onyx",
		slug: "onyx",
		atsScore: "90",
		category: "professional",
		features: "Bold headers",
		description: "Professional template with bold section headers",
	},
	{
		name: "Pikachu",
		slug: "pikachu",
		atsScore: "94",
		category: "modern",
		features: "Popular choice",
		description:
			"Most popular modern template with excellent ATS compatibility",
	},
	{
		name: "Rhyhorn",
		slug: "rhyhorn",
		atsScore: "89",
		category: "robust",
		features: "Strong structure",
		description: "Robust template with strong visual hierarchy",
	},
];
