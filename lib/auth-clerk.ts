import { auth, currentUser } from "@clerk/nextjs/server";
import { eq } from "drizzle-orm";
import { redirect } from "next/navigation";
import { db } from "@/db";
import { users } from "@/db/schema";

export async function getAuthenticatedUser() {
	const { userId } = await auth();

	if (!userId) {
		return null;
	}

	const clerkUser = await currentUser();

	if (!clerkUser) {
		return null;
	}

	// Find or create user in our database
	let user = (
		await db.select().from(users).where(eq(users.clerkId, userId)).limit(1)
	)[0];

	if (!user) {
		const [newUser] = await db
			.insert(users)
			.values({
				clerkId: userId,
				emailAddress: clerkUser.emailAddresses[0]?.emailAddress || "",
			})
			.returning();
		user = newUser;
	}

	return user;
}

export async function requireAuth() {
	const user = await getAuthenticatedUser();

	if (!user) {
		redirect("/ar/sign-in");
	}

	return user;
}
