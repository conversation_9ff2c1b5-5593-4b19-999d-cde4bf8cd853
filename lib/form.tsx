import { Input } from "@heroui/react";

import { AutosaveNestedForm } from "@/components/AutosaveNestedForm";
import CheckboxField from "@/components/CheckboxField";
import DatePickerComponent from "@/components/DatePicker.component";
import { TrixEditorField } from "@/components/TrixEditorField";
import { routes } from "@/config/path-constants";
import { useFormStore } from "@/lib/form-store";

import {Profile, FullResume} from "@/db/schema";

export const renderField = <T extends Record<string, any>>(
	property: string,
	schemaProperty: any,
	index: number,
	item: T,
	schema: string,
) => {
	const { type, label, placeholder, className, description } = schemaProperty;

	const FormField = ({ property: fieldProperty }: { property: string }) => {
		const { formData, handleInputChange, updateField } = useFormStore();

		switch (type) {
			case "string":
				return (
					<Input
						key={fieldProperty}
						{...(label ? { label } : {})}
						{...(description ? { description } : {})}
						className={className}
						name={fieldProperty}
						placeholder={placeholder}
						value={formData[fieldProperty] || ""}
						variant="bordered"
						onChange={handleInputChange}
					/>
				);
			case "number":
				return (
					<Input
						key={fieldProperty}
						{...(label ? { label } : {})}
						{...(description ? { description } : {})}
						className={className}
						name={fieldProperty}
						placeholder={placeholder}
						type="number"
						value={formData[fieldProperty] || ""}
						variant="bordered"
						onChange={handleInputChange}
					/>
				);
			case "date":
				return (
					<DatePickerComponent
						key={fieldProperty}
						{...(label ? { label } : {})}
						{...(description ? { description } : {})}
						className={className}
						defaultValue={formData[fieldProperty] || ""}
						name={fieldProperty}
						onChange={(value) => updateField(fieldProperty, value)}
					/>
				);
			case "boolean":
				return (
					<div className="flex items-center space-x-2 col-span-full">
						<CheckboxField
							defaultSelected={formData[fieldProperty]}
							name={fieldProperty}
							onChange={(value) => updateField(fieldProperty, value)}
						>
							{label}
						</CheckboxField>
					</div>
				);
			case "textarea":
				return (
					<TrixEditorField
						key={fieldProperty}
						{...(description ? { description } : {})}
						className="col-span-full"
						id={`${schema}_${index}_${fieldProperty}`}
						label={label}
						name={fieldProperty}
						value={formData[fieldProperty] || ""}
						onChange={(value) => updateField(fieldProperty, value)}
					/>
				);
			default:
				return null;
		}
	};

	return <FormField property={property} />;
};

export const RenderForm = ({
	schema,
	item,
	index,
}: {
	schema: any;
	item: any;
	index: number;
}) => {
	// Get baseRoute function from the nested forms preparation
	const getBaseRoute = (resumeId: number) => {
		const routeMap: Record<string, (id: number) => string> = {
			educations: routes.educations_url,
			experiences: routes.experiences_url,
			projects: routes.projects_url,
			awards: routes.awards_url,
			certifications: routes.certifications_url,
			skills: routes.skills_url,
			languages: routes.languages_url,
			references: routes.references_url,
			hobbies: routes.hobbies_url,
			volunteerings: routes.volunteerings_url,
			profiles: routes.profiles_url,
		};
		return routeMap[schema.collection]?.(resumeId) || "";
	};

	return (
		<AutosaveNestedForm
			baseRoute={getBaseRoute}
			item={item}
			resumeId={item.resumeId}
		>
			<div className="grid grid-cols-1 w-full md:grid-cols-2 gap-4">
				{Object.entries(schema.properties).map(([property, schemaProperty]) =>
					renderField(property, schemaProperty, index, item, schema.collection),
				)}
			</div>
		</AutosaveNestedForm>
	);
};

export const prepareNestedForms = (resume: FullResume, schemas?: any) => {
	return [
		{
			name: "profile",
			keyName: "network",
			schema: schemas.profilesSchema,
			route: routes.add_profile_url,
			baseRoute: routes.profiles_url,
			items: resume.profiles,
		},
		{
			name: "education",
			keyName: "institution",
			schema: schemas.educationSchema,
			route: routes.add_education_url,
			baseRoute: routes.educations_url,
			items: resume.educations,
		},
		{
			name: "experience",
			keyName: "company",
			schema: schemas.experienceSchema,
			route: routes.add_experience_url,
			baseRoute: routes.experiences_url,
			items: resume.experiences,
		},
		{
			name: "project",
			keyName: "title",
			schema: schemas.projectSchema,
			route: routes.add_project_url,
			baseRoute: routes.projects_url,
			items: resume.projects,
		},
		{
			name: "award",
			keyName: "title",
			schema: schemas.awardSchema,
			route: routes.add_award_url,
			baseRoute: routes.awards_url,
			items: resume.awards,
		},
		{
			name: "certification",
			keyName: "title",
			schema: schemas.certificationSchema,
			route: routes.add_certification_url,
			baseRoute: routes.certifications_url,
			items: resume.certifications,
		},
		{
			name: "skill",
			keyName: "name",
			schema: schemas.skillSchema,
			route: routes.add_skill_url,
			baseRoute: routes.skills_url,
			items: resume.skills,
		},
		{
			name: "language",
			keyName: "name",
			schema: schemas.languageSchema,
			route: routes.add_language_url,
			baseRoute: routes.languages_url,
			items: resume.languages,
			layout: "simple",
		},
		{
			name: "reference",
			keyName: "name",
			schema: schemas.referenceSchema,
			route: routes.add_reference_url,
			baseRoute: routes.references_url,
			items: resume.references,
		},
		{
			name: "hobby",
			keyName: "name",
			schema: schemas.hobbySchema,
			route: routes.add_hobby_url,
			baseRoute: routes.hobbies_url,
			items: resume.hobbies,
			layout: "simple",
		},
		{
			name: "volunteering",
			keyName: "role",
			schema: schemas.volunteeringSchema,
			route: routes.add_volunteering_url,
			baseRoute: routes.volunteerings_url,
			items: resume.volunteerings,
		},
	];
};
