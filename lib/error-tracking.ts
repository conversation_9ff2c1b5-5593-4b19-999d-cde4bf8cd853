import { headers } from "next/headers";

interface ErrorContext {
	userId?: string;
	action?: string;
	component?: string;
	url?: string;
	userAgent?: string;
	[key: string]: any;
}

export async function captureServerError(
	error: Error,
	context: ErrorContext = {},
) {
	try {
		const headersList = await headers();
		const userAgent = headersList.get("user-agent") || undefined;
		const url = headersList.get("referer") || undefined;

		// Log to console for development
		console.error("Server Error:", {
			message: error.message,
			stack: error.stack,
			context: {
				...context,
				url,
				userAgent,
				timestamp: new Date().toISOString(),
			},
		});

		// In production, you could send to PostHog via their REST API or other logging service
		// For now, we'll focus on client-side error tracking
	} catch (logError) {
		console.error("Failed to log error:", logError);
	}
}

export function withErrorTracking<T extends any[], R>(
	fn: (...args: T) => Promise<R>,
	context: ErrorContext = {},
) {
	return async (...args: T): Promise<R> => {
		try {
			return await fn(...args);
		} catch (error) {
			await captureServerError(error as Error, context);
			throw error;
		}
	};
}

// Client-side error tracking
export function captureClientError(error: Error, context: ErrorContext = {}) {
	if (typeof window !== "undefined" && (window as any).posthog) {
		(window as any).posthog.captureException(error, {
			...context,
			timestamp: new Date().toISOString(),
			url: window.location.href,
			userAgent: navigator.userAgent,
		});
	}

	console.error("Client Error:", error, context);
}
