# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Next.js build output
.next
out

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
.next/tsbuildinfo

# OS
.DS_Store
Thumbs.db

# IDEs
.vscode
.idea
*.swp
*.swo

# Testing
coverage

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Git
.git
.gitignore
README.md

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# Development files
scripts/
.claude/
*.md