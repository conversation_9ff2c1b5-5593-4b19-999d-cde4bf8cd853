import { getResume } from "@/actions/resumes";
import { getTemplates } from "@/actions/templates";
import { ResumeCustomizationDrawer } from "@/components/resume/ResumeCustomizationDrawer";
import { ResumeEditForm } from "@/components/resume/resume-edit-form";
import { ResumePreview } from "@/components/resume/resume-preview";
export default async function EditResumePage({
	params,
}: {
	params: Promise<{ id: string }>;
}) {
	const { id } = await params; // Destructure id from params
	const resumeId = Number(id);
	const resume = await getResume(resumeId);

	const templates = await getTemplates();

	if (!resume) {
		return <div>Resume not found</div>;
	}

	return (
		<div className="grid grid-cols-1 lg:grid-cols-12 gap-4 h-full relative">
			<div className="order-2 lg:order-1 col-span-7 h-full">
				<ResumePreview
					className="h-full"
					resume={resume}
					showControls={true}
					showTemplateSelector={true}
					templates={templates}
				/>
			</div>
			<div className="order-1 lg:order-2 col-span-5 w-full overflow-auto space-y-6 p-4">
				<ResumeEditForm data={resume} />
			</div>

			{/* Customization Drawer */}
			<ResumeCustomizationDrawer data={resume} templates={templates} />
		</div>
	);
}
