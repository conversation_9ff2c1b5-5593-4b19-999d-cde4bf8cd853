import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { getResume } from "@/actions/resumes";
import { getResumeWebsite, getWebsiteTemplates } from "@/actions/websites";
import { WebsiteBuilder } from "@/components/website/website-builder";

interface WebsiteBuilderPageProps {
	params: Promise<{
		id: string;
		locale: string;
	}>;
}

export default async function WebsiteBuilderPage({
	params,
}: WebsiteBuilderPageProps) {
	const { id, locale } = await params;
	const resumeId = Number(id);
	const t = await getTranslations({ locale, namespace: "website.builder" });

	// Fetch resume data
	const resume = await getResume(resumeId);
	if (!resume) {
		notFound();
	}

	// Fetch existing website data
	const websiteResult = await getResumeWebsite(resumeId);
	if (!websiteResult.success) {
		// Handle error appropriately
		console.error("Error fetching website:", websiteResult.error);
	}

	// Fetch available website templates
	const templatesResult = await getWebsiteTemplates();
	if (!templatesResult.success) {
		console.error("Error fetching templates:", templatesResult.error);
		notFound();
	}

	return (
		<div className="min-h-screen bg-gray-50">
			<div className="max-w-7xl mx-auto py-8 px-4">
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900 mb-2">
						{t("title")}
					</h1>
					<p className="text-gray-600">{t("description")}</p>
				</div>

				<WebsiteBuilder
					resume={resume}
					existingWebsite={websiteResult.data}
					templates={templatesResult.data || []}
				/>
			</div>
		</div>
	);
}
