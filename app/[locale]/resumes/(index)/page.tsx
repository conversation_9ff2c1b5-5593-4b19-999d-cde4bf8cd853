import { Icon } from "@iconify/react";
import { getTranslations } from "next-intl/server";
import { Suspense } from "react";
import { listResumes } from "@/actions/resumes";
import { getTemplates } from "@/actions/templates";
import ResumeEmptyState from "@/components/resume/resume-empty-state";
import ResumeViewContainer from "@/components/resume/resume-view-container";

// Loading skeleton component
function ResumeCardSkeleton() {
	return (
		<div className="animate-pulse">
			<div className="bg-content2 rounded-lg p-6 space-y-4">
				<div className="flex items-center space-x-3">
					<div className="w-14 h-14 bg-content3 rounded-full" />
					<div className="space-y-2 flex-1">
						<div className="h-4 bg-content3 rounded w-3/4" />
						<div className="h-3 bg-content3 rounded w-1/2" />
					</div>
				</div>
				<div className="h-16 bg-content3 rounded" />
				<div className="flex justify-between items-center">
					<div className="h-3 bg-content3 rounded w-1/4" />
					<div className="flex space-x-2">
						<div className="w-6 h-6 bg-content3 rounded" />
						<div className="w-6 h-6 bg-content3 rounded" />
						<div className="w-6 h-6 bg-content3 rounded" />
					</div>
				</div>
			</div>
		</div>
	);
}

export default async function ResumesPage() {
	const t = await getTranslations("resumes");

	let resumes;
	let templates;
	try {
		[resumes, templates] = await Promise.all([listResumes(), getTemplates()]);
	} catch (error) {
		console.error("Error loading resumes:", error);

		// Check if it's an authentication error
		if (error instanceof Error && error.message === "Authentication required") {
			// This shouldn't happen due to middleware, but handle it gracefully
			console.error(
				"Authentication error in ResumesPage - middleware may have failed",
			);
		}

		return (
			<div className="min-h-[50vh] flex items-center justify-center">
				<div className="text-center">
					<Icon
						className="w-12 h-12 text-danger mx-auto mb-4"
						icon="lucide:alert-circle"
					/>
					<h2 className="text-xl font-semibold text-foreground mb-2">
						{t("error_title")}
					</h2>
					<p className="text-foreground/60">{t("error_description")}</p>
				</div>
			</div>
		);
	}

	if (!resumes) {
		return (
			<div className="min-h-[50vh] flex items-center justify-center">
				<div className="text-center">
					<Icon
						className="w-12 h-12 text-danger mx-auto mb-4"
						icon="lucide:alert-circle"
					/>
					<h2 className="text-xl font-semibold text-foreground mb-2">
						{t("error_title")}
					</h2>
					<p className="text-foreground/60">{t("error_description")}</p>
				</div>
			</div>
		);
	}

	if (resumes.length === 0) {
		return (
			<div className="container mx-auto px-4 py-8">
				<ResumeEmptyState />
			</div>
		);
	}

	return (
		<div className="container mx-auto px-4 py-8">
			<Suspense
				fallback={
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
						{Array.from({ length: 8 }).map((_, i) => (
							<ResumeCardSkeleton key={i} />
						))}
					</div>
				}
			>
				<ResumeViewContainer resumes={resumes} />
			</Suspense>
		</div>
	);
}
