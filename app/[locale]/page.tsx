import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import CTASection from "@/components/homepage/cta-section";
import FeaturesSection from "@/components/homepage/features-section";
import HeroSection from "@/components/homepage/hero-section";
import StatsSection from "@/components/homepage/stats-section";
import StructuredData from "@/components/homepage/structured-data";
import TemplatesShowcase from "@/components/homepage/templates-showcase";
import TestimonialsSection from "@/components/homepage/testimonials-section";

export async function generateMetadata(): Promise<Metadata> {
	const t = await getTranslations("homepage.seo");

	return {
		title: t("title"),
		description: t("description"),
		keywords: t("keywords"),
		openGraph: {
			title: t("title"),
			description: t("description"),
			type: "website",
			url: "https://quickcv.app",
			images: [
				{
					url: "/og-image.jpg",
					width: 1200,
					height: 630,
					alt: t("title"),
				},
			],
		},
		twitter: {
			card: "summary_large_image",
			title: t("title"),
			description: t("description"),
			images: ["/og-image.jpg"],
		},
		metadataBase: new URL("https://quickcv.app"),
		alternates: {
			canonical: "https://quickcv.app",
			languages: {
				"en-US": "https://quickcv.app/en",
				"ar-SA": "https://quickcv.app/ar",
			},
		},
	};
}

export default async function Home() {
	return (
		<>
			<StructuredData />
			<HeroSection />
			<FeaturesSection />
			<TemplatesShowcase />
			<StatsSection />
			<TestimonialsSection />
			<CTASection />
		</>
	);
}
