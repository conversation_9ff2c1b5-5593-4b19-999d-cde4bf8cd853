import { notFound } from "next/navigation";
import { NextIntlClientProvider } from "next-intl";
import { getMessages } from "next-intl/server";
import { getTemplates } from "@/actions/templates";
import Template<PERSON>enderer from "@/components/resume/templates/template-registry";
import { COLOR_SCHEMES, ColorSchemeId } from "@/config/color-schemes";
import "@/styles/globals.css";
import { getResume } from "@/actions/resumes";

interface PDFPageProps {
	params: Promise<{ resumeId: string }>;
	searchParams: Promise<{ userId?: string; locale?: string }>;
}

async function getResumeForPDF(id: number, userId?: string) {
	const whereClause: any = { id };

	// If userId is provided, verify the resume belongs to that user
	if (userId) {
		whereClause.userId = userId;
	}

	return await getResume(id);
}

export default async function PDFPage({ params, searchParams }: PDFPageProps) {
	const { resumeId } = await params;
	const { userId, locale } = await searchParams;
	const id = parseInt(resumeId);

	if (Number.isNaN(id)) {
		notFound();
	}

	// Get locale from URL parameters, fallback to English
	const currentLocale = locale || "en";
	console.log("PDF Page locale:", currentLocale);

	// Load messages for the locale
	let messages: any;
	try {
		messages = await getMessages({ locale: currentLocale });
	} catch (error) {
		console.error("Failed to load messages for PDF:", error);
		// Fallback to minimal English messages
		messages = {
			forms: {
				summary: "Summary",
				experience: "Experience",
				education: "Education",
				skills: "Skills",
				languages: "Languages",
				projects: "Projects",
				certifications: "Certifications",
				profiles: "Profiles",
				references: "References",
				volunteering: "Volunteering",
				awards: "Awards & Achievements",
				hobbies: "Hobbies",
			},
		};
	}

	try {
		const [resume, templates] = await Promise.all([
			getResumeForPDF(id, userId),
			getTemplates(),
		]);

		if (!resume) {
			notFound();
		}

		// Get color scheme from resume data
		const schemeId = (resume.colorScheme || "blue") as ColorSchemeId;
		const colorScheme = COLOR_SCHEMES[schemeId] || COLOR_SCHEMES.blue;

		const cssVariables = {
			"--resume-background": colorScheme.background,
			"--resume-foreground": colorScheme.text,
			"--resume-primary": colorScheme.primary,
		};

		return (
			<>
				<head>
					<link
						href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
						rel="stylesheet"
					/>
					<link
						href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap"
						rel="stylesheet"
					/>
					<link
						href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap"
						rel="stylesheet"
					/>
					<style>{`
            @page {
              size: A4;
              margin: 0;
              padding: 0;
            }
            
            * {
              box-sizing: border-box;
            }
            
            body {
              margin: 0;
              padding: 0;
              background: white;
              font-family: system-ui, -apple-system, sans-serif;
              -webkit-print-color-adjust: exact;
              color-adjust: exact;
              direction: ${currentLocale === "ar" ? "rtl" : "ltr"};
            }
            
            .pdf-container {
              width: 8.5in;
              min-height: 11in;
              margin: 0;
              padding: 0;
              background: white;
              position: relative;
              direction: ${currentLocale === "ar" ? "rtl" : "ltr"};
            }
            
            .no-print,
            .preview-controls {
              display: none !important;
            }
          `}</style>
				</head>

				<NextIntlClientProvider messages={messages} locale={currentLocale}>
					<div
						className="pdf-container"
						id="resume-preview"
						style={cssVariables as React.CSSProperties}
						dir={currentLocale === "ar" ? "rtl" : "ltr"}
					>
						<TemplateRenderer
							resume={resume as any}
							templates={templates}
							className="w-full h-full"
						/>
					</div>
				</NextIntlClientProvider>
			</>
		);
	} catch (error) {
		console.error("Error rendering PDF page:", error);
		notFound();
	}
}
