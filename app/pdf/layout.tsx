import React from "react";

interface PDFLayoutProps {
	children: React.ReactNode;
}

export default function PDFLayout({ children }: PDFLayoutProps) {
	return (
		<html lang="en" dir="ltr">
			<head>
				<meta charSet="utf-8" />
				<meta name="viewport" content="width=device-width, initial-scale=1" />
				<title>PDF Export</title>
			</head>
			<body className="bg-white">{children}</body>
		</html>
	);
}
