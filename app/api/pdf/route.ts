import { and, eq } from "drizzle-orm";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { resumes } from "@/db/schema";
import { requireAuth } from "@/lib/auth-clerk";

// Dynamic import for <PERSON><PERSON>peteer to avoid bundling issues
async function generatePDF(url: string) {
	try {
		const puppeteer = await import("puppeteer");

		// Configure browser launch options based on environment
		const isProduction = process.env.NODE_ENV === "production";
		const isVercel = process.env.VERCEL === "1";

		const browserOptions: any = {
			headless: true,
			args: [
				"--no-sandbox",
				"--disable-setuid-sandbox",
				"--disable-dev-shm-usage",
				"--disable-web-security",
				"--font-render-hinting=none",
				"--disable-gpu",
				"--disable-background-timer-throttling",
				"--disable-backgrounding-occluded-windows",
				"--disable-renderer-backgrounding",
				"--disable-features=VizDisplayCompositor",
			],
		};

		// In production/Vercel, use default Chromium (no executablePath)
		// In development, try to use system Chrome if available
		if (!isProduction && !isVercel) {
			const possiblePaths = [
				process.env.CHROME_EXECUTABLE_PATH,
				"/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", // macOS
				"/usr/bin/google-chrome", // Linux
				"/usr/bin/chromium-browser", // Linux Chromium
				"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", // Windows
				"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe", // Windows 32-bit
			].filter(Boolean);

			for (const path of possiblePaths) {
				try {
					const fs = await import("node:fs");
					if (path && fs.existsSync(path)) {
						browserOptions.executablePath = path;
						console.log(`Using Chrome at: ${path}`);
						break;
					}
				} catch {
					// Continue to next path
				}
			}
		}

		console.log(
			`Launching browser in ${isProduction ? "production" : "development"} mode`,
		);
		const browser = await puppeteer.default.launch(browserOptions);

		const page = await browser.newPage();

		// Set viewport for consistent rendering
		await page.setViewport({
			width: 1280,
			height: 1024,
			deviceScaleFactor: 2,
		});

		// Navigate to the PDF rendering page
		await page.goto(url, {
			waitUntil: ["networkidle0", "domcontentloaded"],
			timeout: 30000,
		});

		// Wait for fonts to load
		await page.evaluateHandle("document.fonts.ready");

		// Generate PDF
		const pdf = await page.pdf({
			format: "A4",
			printBackground: true,
			margin: {
				top: 0,
				right: 0,
				bottom: 0,
				left: 0,
			},
			preferCSSPageSize: true,
		});

		await browser.close();
		return pdf;
	} catch (error) {
		console.error("PDF generation error:", error);
		throw new Error("Failed to generate PDF");
	}
}

export async function POST(request: NextRequest) {
	try {
		const user = await requireAuth();
		const { resumeId, locale } = await request.json();

		if (!resumeId) {
			return NextResponse.json(
				{ error: "Resume ID is required" },
				{ status: 400 },
			);
		}

		// Verify the resume belongs to the user
		const [resume] = await db
			.select()
			.from(resumes)
			.where(and(eq(resumes.id, resumeId), eq(resumes.userId, user.clerkId)))
			.limit(1);

		if (!resume) {
			return NextResponse.json({ error: "Resume not found" }, { status: 404 });
		}

		// Get the base URL for the PDF rendering route
		const protocol = request.nextUrl.protocol;
		const host = request.nextUrl.host;
		const baseUrl = `${protocol}//${host}`;

		const pdfUrl = `${baseUrl}/pdf/${resumeId}?userId=${user.clerkId}&locale=${locale || "en"}`;

		// Generate PDF
		const pdfBuffer = await generatePDF(pdfUrl);

		// Create response with PDF
		const response = new NextResponse(pdfBuffer, {
			status: 200,
			headers: {
				"Content-Type": "application/pdf",
				"Content-Disposition": `attachment; filename="${resume.title || "resume"}_${new Date().toISOString().slice(0, 10)}.pdf"`,
				"Cache-Control": "no-cache",
			},
		});

		return response;
	} catch (error) {
		console.error("PDF export error:", error);
		return NextResponse.json(
			{ error: "Failed to export PDF" },
			{ status: 500 },
		);
	}
}
