import { NextRequest, NextResponse } from "next/server";
import { UTApi } from "uploadthing/server";
import { requireAuth } from "@/lib/auth-clerk";

const utapi = new UTApi();

export async function DELETE(request: NextRequest) {
	try {
		// Verify authentication
		await requireAuth();

		const body = await request.json();
		const { fileUrl } = body;

		if (!fileUrl || typeof fileUrl !== "string") {
			return NextResponse.json(
				{ error: "File URL is required" },
				{ status: 400 },
			);
		}

		// Extract file key from URL
		// UploadThing URLs are in format: https://utfs.io/f/{fileKey}
		const fileKey = fileUrl.split("/").pop();

		if (!fileKey) {
			return NextResponse.json({ error: "Invalid file URL" }, { status: 400 });
		}

		// Delete file from UploadThing
		await utapi.deleteFiles([fileKey]);

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error deleting file from UploadThing:", error);
		return NextResponse.json(
			{ error: "Failed to delete file" },
			{ status: 500 },
		);
	}
}
