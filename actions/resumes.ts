"use server";

import { and, asc, desc, eq } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import { db } from "@/db";
import {
	awards,
	certifications,
	educations,
	experiences,
	hobbies,
	languages,
	profiles,
	projects,
	references,
	resumes,
	skills,
	templates,
	volunteerings,
} from "@/db/schema";
import { requireAuth } from "@/lib/auth-clerk";

export async function createResume(resumeData: any) {
	try {
		const user = await requireAuth();

		const title = resumeData?.title || "New Resume";

		// Create the main resume
		const [resume] = await db
			.insert(resumes)
			.values({
				title,
				userId: user.clerkId,
				templateId: 1, // Default template
				...resumeData,
			})
			.returning();

		// Create empty nested resource records
		await Promise.all([
			db.insert(educations).values({ resumeId: resume.id }),
			db.insert(experiences).values({ resumeId: resume.id }),
			db.insert(projects).values({ resumeId: resume.id }),
			db.insert(awards).values({ resumeId: resume.id }),
			db.insert(certifications).values({ resumeId: resume.id }),
			db.insert(skills).values({ resumeId: resume.id }),
			db.insert(languages).values({ resumeId: resume.id }),
			db.insert(references).values({ resumeId: resume.id }),
			db.insert(hobbies).values({ resumeId: resume.id }),
			db.insert(volunteerings).values({ resumeId: resume.id }),
			db.insert(profiles).values({ resumeId: resume.id }),
		]);

		// Return resume with nested data structure for compatibility
		return {
			...resume,
			educations: [{ resumeId: resume.id }],
			experiences: [{ resumeId: resume.id }],
			projects: [{ resumeId: resume.id }],
			awards: [{ resumeId: resume.id }],
			certifications: [{ resumeId: resume.id }],
			skills: [{ resumeId: resume.id }],
			languages: [{ resumeId: resume.id }],
			references: [{ resumeId: resume.id }],
			hobbies: [{ resumeId: resume.id }],
			volunteerings: [{ resumeId: resume.id }],
			profiles: [{ resumeId: resume.id }],
		};
	} catch (error) {
		console.error("Error creating resume:", error);
		throw new Error("Failed to create resume");
	}
}

export async function getResume(id: number) {
	try {
		const user = await requireAuth();

		// Get the main resume with template
		const [resume] = await db
			.select()
			.from(resumes)
			.leftJoin(templates, eq(resumes.templateId, templates.id))
			.where(and(eq(resumes.id, id), eq(resumes.userId, user.clerkId)))
			.limit(1);

		if (!resume.resumes) {
			throw new Error("Resume not found");
		}

		// Get all nested resources in parallel with proper ordering
		const [
			resumeEducations,
			resumeExperiences,
			resumeProjects,
			resumeAwards,
			resumeCertifications,
			resumeSkills,
			resumeLanguages,
			resumeReferences,
			resumeHobbies,
			resumeVolunteerings,
			resumeProfiles,
		] = await Promise.all([
			db
				.select()
				.from(educations)
				.where(eq(educations.resumeId, id))
				.orderBy(asc(educations.sort)),
			db
				.select()
				.from(experiences)
				.where(eq(experiences.resumeId, id))
				.orderBy(asc(experiences.sort)),
			db
				.select()
				.from(projects)
				.where(eq(projects.resumeId, id))
				.orderBy(asc(projects.sort)),
			db
				.select()
				.from(awards)
				.where(eq(awards.resumeId, id))
				.orderBy(asc(awards.sort)),
			db
				.select()
				.from(certifications)
				.where(eq(certifications.resumeId, id))
				.orderBy(asc(certifications.sort)),
			db
				.select()
				.from(skills)
				.where(eq(skills.resumeId, id))
				.orderBy(asc(skills.sort)),
			db
				.select()
				.from(languages)
				.where(eq(languages.resumeId, id))
				.orderBy(asc(languages.sort)),
			db
				.select()
				.from(references)
				.where(eq(references.resumeId, id))
				.orderBy(asc(references.sort)),
			db
				.select()
				.from(hobbies)
				.where(eq(hobbies.resumeId, id))
				.orderBy(asc(hobbies.sort)),
			db
				.select()
				.from(volunteerings)
				.where(eq(volunteerings.resumeId, id))
				.orderBy(asc(volunteerings.sort)),
			db
				.select()
				.from(profiles)
				.where(eq(profiles.resumeId, id))
				.orderBy(asc(profiles.sort)),
		]);

		// Combine the data to match the original structure
		const fullResume = {
			...resume.resumes,
			template: resume.templates,
			educations: resumeEducations,
			experiences: resumeExperiences,
			projects: resumeProjects,
			awards: resumeAwards,
			certifications: resumeCertifications,
			skills: resumeSkills,
			languages: resumeLanguages,
			references: resumeReferences,
			hobbies: resumeHobbies,
			volunteerings: resumeVolunteerings,
			profiles: resumeProfiles,
		};

		return {
			...transformResumeForFrontend(fullResume),
			template_name: resume.templates?.name,
		};
	} catch (error) {
		console.error("Error fetching resume:", error);
		throw error;
	}
}

// Transform database data to match frontend types
function transformResumeForFrontend(resume: any) {
	return {
		...resume,
		firstName: resume.firstName,
		lastName: resume.lastName,
		jobTitle: resume.jobTitle,
		birthDate: resume.birthDate,
		showPhoto: resume.showPhoto,
		colorScheme: resume.colorScheme,
		font_family: resume.fontFamily,
		custom_primary_color: resume.customPrimaryColor,
		custom_text_color: resume.customTextColor,
		custom_background_color: resume.customBackgroundColor,
		photo: resume.photo,
		thumbnail: resume.thumbnail,
		created_at: resume.createdAt,
		updated_at: resume.updatedAt,
		template_id: resume.templateId,
		userId: resume.userId,
	};
}

export async function listResumes() {
	try {
		const user = await requireAuth();

		const resumeList = await db
			.select()
			.from(resumes)
			.leftJoin(templates, eq(resumes.templateId, templates.id))
			.where(eq(resumes.userId, user.clerkId))
			.orderBy(desc(resumes.updatedAt));

		return resumeList.map((row) => ({
			...transformResumeForFrontend(row.resumes),
			template_name: row.templates?.name,
		}));
	} catch (error) {
		console.error("Error fetching resumes:", error);
		throw error;
	}
}

export async function updateResume(formData: FormData) {
	try {
		const user = await requireAuth();
		const id = parseInt(formData.get("id") as string);

		if (!id) {
			throw new Error("Resume ID is required");
		}

		// Parse form data
		const updateData: any = {};

		// Handle text fields
		const textFields = [
			"title",
			"firstName",
			"lastName",
			"jobTitle",
			"email",
			"website",
			"bio",
			"city",
			"street",
			"country",
			"address",
			"photo",
			"thumbnail",
			"colorScheme",
			"fontFamily",
			"spacing",
			"margins",
		];

		console.log(formData, "FormData in updateResume");

		textFields.forEach((field) => {
			const value = formData.get(field);
			if (value !== null && value !== undefined) {
				updateData[field] = value.toString();
			}
		});

		// Handle boolean fields
		const showPhoto = formData.get("showPhoto");
		if (showPhoto !== null) {
			updateData.showPhoto = showPhoto === "true";
		}

		// Handle template ID
		const templateId = formData.get("templateId");
		if (templateId) {
			updateData.templateId = parseInt(templateId.toString());
		}

		const result = await db
			.update(resumes)
			.set(updateData)
			.where(and(eq(resumes.id, id), eq(resumes.userId, user.clerkId)))
			.returning();

		if (result.length === 0) {
			throw new Error("Resume not found or unauthorized");
		}

		revalidatePath(`/resumes/edit/${id}`);

		return {
			success: true,
			data: await getResume(id),
		};
	} catch (error) {
		console.error("Error updating resume:", error);
		return {
			success: false,
			errors: {
				fieldErrors: {},
				formErrors: [error instanceof Error ? error.message : "Update failed"],
			},
		};
	}
}

export async function deleteResume(id: number) {
	try {
		const user = await requireAuth();

		const result = await db
			.delete(resumes)
			.where(and(eq(resumes.id, id), eq(resumes.userId, user.clerkId)))
			.returning();

		if (result.length === 0) {
			throw new Error("Resume not found or unauthorized");
		}

		revalidatePath("/resumes");
	} catch (error) {
		console.error("Error deleting resume:", error);
		throw error;
	}
}

// Server action adapter for react-autosave compatibility
export async function updateResumeForAutosave(
	resumeData: any,
): Promise<boolean> {
	try {
		const user = await requireAuth();

		if (!resumeData.id) {
			throw new Error("Resume ID is required");
		}

		const updateData: any = {};

		// Handle text fields
		const textFields = [
			"title",
			"firstName",
			"lastName",
			"jobTitle",
			"email",
			"website",
			"bio",
			"city",
			"street",
			"country",
			"address",
			"photo",
			"colorScheme",
			"fontFamily",
			"spacing",
			"margins",
		];

		textFields.forEach((field) => {
			if (resumeData[field] !== undefined && resumeData[field] !== null) {
				updateData[field] = resumeData[field].toString();
			}
		});

		// Handle boolean fields
		if (resumeData.showPhoto !== undefined) {
			updateData.showPhoto = Boolean(resumeData.showPhoto);
		}

		// Handle template ID
		if (resumeData.templateId !== undefined) {
			updateData.templateId = parseInt(resumeData.templateId.toString());
		}
		// Also handle template_id field name (for compatibility)
		if (resumeData.template_id !== undefined) {
			updateData.templateId = parseInt(resumeData.template_id.toString());
		}

		// Handle birthDate
		if (resumeData.birthDate) {
			updateData.birthDate = new Date(resumeData.birthDate);
		}

		const result = await db
			.update(resumes)
			.set(updateData)
			.where(
				and(
					eq(resumes.id, parseInt(resumeData.id.toString())),
					eq(resumes.userId, user.clerkId),
				),
			)
			.returning();

		if (result.length === 0) {
			throw new Error("Resume not found or unauthorized");
		}

		// Revalidate the page to reflect changes
		revalidatePath(`/resumes/edit/${resumeData.id}`);

		return true;
	} catch (error) {
		console.error("Error updating resume for autosave:", error);
		return false;
	}
}

// Add nested item functions
export async function addNestedItem(route: string, resumeId: number | string) {
	try {
		const user = await requireAuth();

		// Verify the resume belongs to the user
		const [resume] = await db
			.select()
			.from(resumes)
			.where(
				and(
					eq(resumes.id, parseInt(resumeId.toString())),
					eq(resumes.userId, user.clerkId),
				),
			)
			.limit(1);

		if (!resume) {
			throw new Error("Resume not found or unauthorized");
		}

		// Parse the route to determine the resource type
		const resourceType = route.split("/").pop();
		const resumeIdNum = parseInt(resumeId.toString());

		let result;

		switch (resourceType) {
			case "educations":
				[result] = await db
					.insert(educations)
					.values({ resumeId: resumeIdNum })
					.returning();
				break;
			case "experiences":
				[result] = await db
					.insert(experiences)
					.values({ resumeId: resumeIdNum })
					.returning();
				break;
			case "projects":
				[result] = await db
					.insert(projects)
					.values({ resumeId: resumeIdNum })
					.returning();
				break;
			case "awards":
				[result] = await db
					.insert(awards)
					.values({ resumeId: resumeIdNum })
					.returning();
				break;
			case "certifications":
				[result] = await db
					.insert(certifications)
					.values({ resumeId: resumeIdNum })
					.returning();
				break;
			case "skills":
				[result] = await db
					.insert(skills)
					.values({ resumeId: resumeIdNum })
					.returning();
				break;
			case "languages":
				[result] = await db
					.insert(languages)
					.values({ resumeId: resumeIdNum })
					.returning();
				break;
			case "references":
				[result] = await db
					.insert(references)
					.values({ resumeId: resumeIdNum })
					.returning();
				break;
			case "hobbies":
				[result] = await db
					.insert(hobbies)
					.values({ resumeId: resumeIdNum })
					.returning();
				break;
			case "volunteerings":
				[result] = await db
					.insert(volunteerings)
					.values({ resumeId: resumeIdNum })
					.returning();
				break;
			case "profiles":
				[result] = await db
					.insert(profiles)
					.values({ resumeId: resumeIdNum })
					.returning();
				break;
			default:
				throw new Error(`Unknown resource type: ${resourceType}`);
		}

		revalidatePath(`/resumes/edit/${resumeId}`);
		return { success: true, data: result };
	} catch (error) {
		console.error("Error adding nested item:", error);
		return { success: false, error: "Failed to add item." };
	}
}

export async function updateNestedItem(
	route: string,
	id: number | string,
	resumeId: number | string,
	data: any,
) {
	try {
		const user = await requireAuth();

		// Verify the resume belongs to the user
		const [resume] = await db
			.select()
			.from(resumes)
			.where(
				and(
					eq(resumes.id, parseInt(resumeId.toString())),
					eq(resumes.userId, user.clerkId),
				),
			)
			.limit(1);

		if (!resume) {
			throw new Error("Resume not found or unauthorized");
		}

		// Parse the route to determine the resource type
		const resourceType = route.split("/").filter(Boolean).pop();
		const itemId = parseInt(id.toString());

		// Remove undefined/null values and prepare update data
		const updateData = Object.fromEntries(
			Object.entries(data).filter(
				([, value]) => value !== undefined && value !== null,
			),
		);

		let result;

		switch (resourceType) {
			case "educations":
				[result] = await db
					.update(educations)
					.set(updateData)
					.where(eq(educations.id, itemId))
					.returning();
				break;
			case "experiences":
				[result] = await db
					.update(experiences)
					.set(updateData)
					.where(eq(experiences.id, itemId))
					.returning();
				break;
			case "projects":
				[result] = await db
					.update(projects)
					.set(updateData)
					.where(eq(projects.id, itemId))
					.returning();
				break;
			case "awards":
				[result] = await db
					.update(awards)
					.set(updateData)
					.where(eq(awards.id, itemId))
					.returning();
				break;
			case "certifications":
				[result] = await db
					.update(certifications)
					.set(updateData)
					.where(eq(certifications.id, itemId))
					.returning();
				break;
			case "skills":
				[result] = await db
					.update(skills)
					.set(updateData)
					.where(eq(skills.id, itemId))
					.returning();
				break;
			case "languages":
				[result] = await db
					.update(languages)
					.set(updateData)
					.where(eq(languages.id, itemId))
					.returning();
				break;
			case "references":
				[result] = await db
					.update(references)
					.set(updateData)
					.where(eq(references.id, itemId))
					.returning();
				break;
			case "hobbies":
				[result] = await db
					.update(hobbies)
					.set(updateData)
					.where(eq(hobbies.id, itemId))
					.returning();
				break;
			case "volunteerings":
				[result] = await db
					.update(volunteerings)
					.set(updateData)
					.where(eq(volunteerings.id, itemId))
					.returning();
				break;
			case "profiles":
				[result] = await db
					.update(profiles)
					.set(updateData)
					.where(eq(profiles.id, itemId))
					.returning();
				break;
			default:
				throw new Error(`Unknown resource type: ${resourceType}`);
		}

		revalidatePath(`/resumes/edit/${resumeId}`);
		return { success: true, data: result };
	} catch (error) {
		console.error("Error updating nested item:", error);
		return { success: false, error: "Failed to update item." };
	}
}

export async function deleteNestedItem(
	route: string,
	id: number | string,
	resumeId: number | string,
) {
	try {
		const user = await requireAuth();

		// Verify the resume belongs to the user
		const [resume] = await db
			.select()
			.from(resumes)
			.where(
				and(
					eq(resumes.id, parseInt(resumeId.toString())),
					eq(resumes.userId, user.clerkId),
				),
			)
			.limit(1);

		if (!resume) {
			throw new Error("Resume not found or unauthorized");
		}

		// Parse the route to determine the resource type
		const resourceType = route.split("/").filter(Boolean).pop();
		const itemId = parseInt(id.toString());

		switch (resourceType) {
			case "educations":
				await db.delete(educations).where(eq(educations.id, itemId));
				break;
			case "experiences":
				await db.delete(experiences).where(eq(experiences.id, itemId));
				break;
			case "projects":
				await db.delete(projects).where(eq(projects.id, itemId));
				break;
			case "awards":
				await db.delete(awards).where(eq(awards.id, itemId));
				break;
			case "certifications":
				await db.delete(certifications).where(eq(certifications.id, itemId));
				break;
			case "skills":
				await db.delete(skills).where(eq(skills.id, itemId));
				break;
			case "languages":
				await db.delete(languages).where(eq(languages.id, itemId));
				break;
			case "references":
				await db.delete(references).where(eq(references.id, itemId));
				break;
			case "hobbies":
				await db.delete(hobbies).where(eq(hobbies.id, itemId));
				break;
			case "volunteerings":
				await db.delete(volunteerings).where(eq(volunteerings.id, itemId));
				break;
			case "profiles":
				await db.delete(profiles).where(eq(profiles.id, itemId));
				break;
			default:
				throw new Error(`Unknown resource type: ${resourceType}`);
		}

		revalidatePath(`/resumes/edit/${resumeId}`);
		return { success: true };
	} catch (error) {
		console.error("Error deleting nested item:", error);
		return { success: false, error: "Failed to delete item." };
	}
}

// Function to save resume thumbnail
export async function saveResumeThumbnail(
	resumeId: number,
	thumbnailBase64: string,
) {
	try {
		const user = await requireAuth();

		// Verify the resume belongs to the user
		const [resume] = await db
			.select()
			.from(resumes)
			.where(and(eq(resumes.id, resumeId), eq(resumes.userId, user.clerkId)))
			.limit(1);

		if (!resume) {
			throw new Error("Resume not found or unauthorized");
		}

		// Update the resume with the new thumbnail
		await db
			.update(resumes)
			.set({ thumbnail: thumbnailBase64 })
			.where(eq(resumes.id, resumeId));

		revalidatePath(`/resumes`);
		revalidatePath(`/resumes/edit/${resumeId}`);

		return { success: true };
	} catch (error) {
		console.error("Error saving resume thumbnail:", error);
		return { success: false, error: "Failed to save thumbnail." };
	}
}

// Function to duplicate a resume
export async function duplicateResume(resumeId: number) {
	try {
		const user = await requireAuth();

		// Get the original resume
		const [originalResume] = await db
			.select()
			.from(resumes)
			.where(and(eq(resumes.id, resumeId), eq(resumes.userId, user.clerkId)))
			.limit(1);

		if (!originalResume) {
			throw new Error("Resume not found or unauthorized");
		}

		// Get all nested resources in parallel
		const [
			originalEducations,
			originalExperiences,
			originalProjects,
			originalAwards,
			originalCertifications,
			originalSkills,
			originalLanguages,
			originalReferences,
			originalHobbies,
			originalVolunteerings,
			originalProfiles,
		] = await Promise.all([
			db.select().from(educations).where(eq(educations.resumeId, resumeId)),
			db.select().from(experiences).where(eq(experiences.resumeId, resumeId)),
			db.select().from(projects).where(eq(projects.resumeId, resumeId)),
			db.select().from(awards).where(eq(awards.resumeId, resumeId)),
			db
				.select()
				.from(certifications)
				.where(eq(certifications.resumeId, resumeId)),
			db.select().from(skills).where(eq(skills.resumeId, resumeId)),
			db.select().from(languages).where(eq(languages.resumeId, resumeId)),
			db.select().from(references).where(eq(references.resumeId, resumeId)),
			db.select().from(hobbies).where(eq(hobbies.resumeId, resumeId)),
			db
				.select()
				.from(volunteerings)
				.where(eq(volunteerings.resumeId, resumeId)),
			db.select().from(profiles).where(eq(profiles.resumeId, resumeId)),
		]);

		// Create the new resume with " copy" appended to the title
		const newResumeData = {
			title: `${originalResume.title} copy`,
			firstName: originalResume.firstName,
			lastName: originalResume.lastName,
			jobTitle: originalResume.jobTitle,
			email: originalResume.email,
			website: originalResume.website,
			bio: originalResume.bio,
			address: originalResume.address,
			city: originalResume.city,
			street: originalResume.street,
			country: originalResume.country,
			birthDate: originalResume.birthDate,
			showPhoto: originalResume.showPhoto,
			photo: originalResume.photo,
			colorScheme: originalResume.colorScheme,
			fontFamily: originalResume.fontFamily,
			spacing: originalResume.spacing,
			margins: originalResume.margins,
			templateId: originalResume.templateId,
			userId: user.clerkId,
		};

		// Create the new resume
		const [newResume] = await db
			.insert(resumes)
			.values(newResumeData)
			.returning();

		// Duplicate all nested resources
		const nestedResourcesData = [
			{ collection: originalEducations, table: educations, name: "educations" },
			{
				collection: originalExperiences,
				table: experiences,
				name: "experiences",
			},
			{ collection: originalProjects, table: projects, name: "projects" },
			{ collection: originalAwards, table: awards, name: "awards" },
			{
				collection: originalCertifications,
				table: certifications,
				name: "certifications",
			},
			{ collection: originalSkills, table: skills, name: "skills" },
			{ collection: originalLanguages, table: languages, name: "languages" },
			{ collection: originalReferences, table: references, name: "references" },
			{ collection: originalHobbies, table: hobbies, name: "hobbies" },
			{
				collection: originalVolunteerings,
				table: volunteerings,
				name: "volunteerings",
			},
			{ collection: originalProfiles, table: profiles, name: "profiles" },
		];

		// Create nested resources for the new resume
		for (const { collection, table, name } of nestedResourcesData) {
			if (collection && collection.length > 0) {
				// Create multiple records for collections with existing data
				const createData = collection.map((item: any) => {
					const { id, createdAt, updatedAt, ...itemData } = item;
					return {
						...itemData,
						resumeId: newResume.id,
					};
				});

				await db.insert(table).values(createData);
			} else {
				// Create empty record for collections with no data
				await db.insert(table).values({ resumeId: newResume.id });
			}
		}

		revalidatePath("/resumes");
		return { success: true, resumeId: newResume.id };
	} catch (error) {
		console.error("Error duplicating resume:", error);
		return { success: false, error: "Failed to duplicate resume." };
	}
}
