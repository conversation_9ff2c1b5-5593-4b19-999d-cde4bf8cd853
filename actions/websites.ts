"use server";

import { and, asc, desc, eq } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import { db } from "@/db";
import {
	awards,
	certifications,
	educations,
	experiences,
	hobbies,
	languages,
	profiles,
	projects,
	Resume,
	references,
	resumes,
	skills,
	volunteerings,
	websites,
	websiteTemplates,
} from "@/db/schema";
import { requireAuth } from "@/lib/auth-clerk";

// Create a new website for a resume
export async function createWebsite(
	resumeId: number,
	websiteTemplateId: number,
	settings: {
		slug: string;
	},
) {
	try {
		const user = await requireAuth();

		// Verify the resume belongs to the user
		const [resume] = await db
			.select()
			.from(resumes)
			.where(and(eq(resumes.id, resumeId), eq(resumes.userId, user.clerkId)))
			.limit(1);

		if (!resume) {
			throw new Error("Resume not found or unauthorized");
		}

		// Check if slug is already taken
		const [existingWebsite] = await db
			.select()
			.from(websites)
			.where(eq(websites.slug, settings.slug))
			.limit(1);

		if (existingWebsite) {
			throw new Error(
				"This URL slug is already taken. Please choose a different one.",
			);
		}

		// Verify website template exists
		const [websiteTemplate] = await db
			.select()
			.from(websiteTemplates)
			.where(eq(websiteTemplates.id, websiteTemplateId))
			.limit(1);

		if (!websiteTemplate) {
			throw new Error("Website template not found");
		}

		// Create the website
		const [newWebsite] = await db
			.insert(websites)
			.values({
				slug: settings.slug,
				userId: user.clerkId,
				resumeId: resumeId,
				websiteTemplateId: websiteTemplateId,
			})
			.returning();

		// Get the created website with relations for return
		const [websiteWithRelations] = await db
			.select()
			.from(websites)
			.leftJoin(resumes, eq(websites.resumeId, resumes.id))
			.leftJoin(
				websiteTemplates,
				eq(websites.websiteTemplateId, websiteTemplates.id),
			)
			.where(eq(websites.id, newWebsite.id))
			.limit(1);

		const website = {
			...websiteWithRelations.websites,
			resume: websiteWithRelations.resumes,
			websiteTemplate: websiteWithRelations.website_templates,
		};

		revalidatePath(`/resumes`);
		revalidatePath(`/resumes/edit/${resumeId}`);

		return {
			success: true,
			data: website,
		};
	} catch (error) {
		console.error("Error creating website:", error);
		return {
			success: false,
			error:
				error instanceof Error ? error.message : "Failed to create website",
		};
	}
}

export async function getUserWebsites() {
	try {
		const user = await requireAuth();

		const userWebsites = await db
			.select({
				id: websites.id,
				slug: websites.slug,
				isPublic: websites.isPublic,
				analytics: websites.analytics,
				backgroundPattern: websites.backgroundPattern,
				createdAt: websites.createdAt,
				updatedAt: websites.updatedAt,
				userId: websites.userId,
				resumeId: websites.resumeId,
				websiteTemplateId: websites.websiteTemplateId,
				resume: {
					id: resumes.id,
					title: resumes.title,
					firstName: resumes.firstName,
					lastName: resumes.lastName,
					jobTitle: resumes.jobTitle,
				},
				websiteTemplate: websiteTemplates,
			})
			.from(websites)
			.leftJoin(resumes, eq(websites.resumeId, resumes.id))
			.leftJoin(
				websiteTemplates,
				eq(websites.websiteTemplateId, websiteTemplates.id),
			)
			.where(eq(websites.userId, user.clerkId))
			.orderBy(desc(websites.updatedAt));

		return {
			success: true,
			data: userWebsites,
		};
	} catch (error) {
		console.error("Error fetching user websites:", error);
		return {
			success: false,
			error: "Failed to fetch websites",
		};
	}
}

// Update website settings
export async function updateWebsite(
	websiteId: number,
	settings: {
		slug?: string;
		websiteTemplateId?: number;
	},
) {
	try {
		const user = await requireAuth();

		// Verify the website belongs to the user
		const [existingWebsite] = await db
			.select()
			.from(websites)
			.where(and(eq(websites.id, websiteId), eq(websites.userId, user.clerkId)))
			.limit(1);

		if (!existingWebsite) {
			throw new Error("Website not found or unauthorized");
		}

		// If slug is being updated, check if it's available
		if (settings.slug && settings.slug !== existingWebsite.slug) {
			const [slugExists] = await db
				.select()
				.from(websites)
				.where(eq(websites.slug, settings.slug))
				.limit(1);

			if (slugExists) {
				throw new Error(
					"This URL slug is already taken. Please choose a different one.",
				);
			}
		}

		// If website template is being updated, verify it exists
		if (settings.websiteTemplateId) {
			const [websiteTemplate] = await db
				.select()
				.from(websiteTemplates)
				.where(eq(websiteTemplates.id, settings.websiteTemplateId))
				.limit(1);

			if (!websiteTemplate) {
				throw new Error("Website template not found");
			}
		}

		// Prepare update data
		const updateData: any = {};
		if (settings.slug) updateData.slug = settings.slug;
		if (settings.websiteTemplateId)
			updateData.websiteTemplateId = settings.websiteTemplateId;

		// Update the website
		const [updatedWebsite] = await db
			.update(websites)
			.set(updateData)
			.where(eq(websites.id, websiteId))
			.returning();

		// Get the updated website with relations
		const [websiteWithRelations] = await db
			.select()
			.from(websites)
			.leftJoin(resumes, eq(websites.resumeId, resumes.id))
			.leftJoin(
				websiteTemplates,
				eq(websites.websiteTemplateId, websiteTemplates.id),
			)
			.where(eq(websites.id, websiteId))
			.limit(1);

		const finalWebsite = {
			...websiteWithRelations.websites,
			resume: websiteWithRelations.resumes,
			websiteTemplate: websiteWithRelations.website_templates,
		};

		revalidatePath(`/resumes`);
		revalidatePath(`/resumes/edit/${updatedWebsite.resumeId}`);
		revalidatePath(`/resumes/${updatedWebsite.resumeId}/website`);

		// Revalidate public website if it's published
		if (updatedWebsite.isPublic) {
			revalidatePath(`/cv/${updatedWebsite.slug}`);
		}

		return {
			success: true,
			data: finalWebsite,
		};
	} catch (error) {
		console.error("Error updating website:", error);
		return {
			success: false,
			error:
				error instanceof Error ? error.message : "Failed to update website",
		};
	}
}

// Get public website by slug (no auth required)
export async function getPublicWebsite(slug: string) {
	try {
		// Get the website with basic relations
		const [websiteData] = await db
			.select()
			.from(websites)
			.leftJoin(resumes, eq(websites.resumeId, resumes.id))
			.leftJoin(
				websiteTemplates,
				eq(websites.websiteTemplateId, websiteTemplates.id),
			)
			.where(and(eq(websites.slug, slug), eq(websites.isPublic, true)))
			.limit(1);

		if (!websiteData.websites) {
			return {
				success: false,
				error: "Website not found or not public",
			};
		}

		const resumeId = websiteData.resumes?.id;
		if (!resumeId) {
			return {
				success: false,
				error: "Resume data not found",
			};
		}

		// Get all nested resume resources in parallel (excluding references for privacy)
		const [
			resumeEducations,
			resumeExperiences,
			resumeProjects,
			resumeAwards,
			resumeCertifications,
			resumeSkills,
			resumeLanguages,
			resumeHobbies,
			resumeVolunteerings,
			resumeProfiles,
		] = await Promise.all([
			db
				.select()
				.from(educations)
				.where(eq(educations.resumeId, resumeId))
				.orderBy(asc(educations.sort)),
			db
				.select()
				.from(experiences)
				.where(eq(experiences.resumeId, resumeId))
				.orderBy(asc(experiences.sort)),
			db
				.select()
				.from(projects)
				.where(eq(projects.resumeId, resumeId))
				.orderBy(asc(projects.sort)),
			db
				.select()
				.from(awards)
				.where(eq(awards.resumeId, resumeId))
				.orderBy(asc(awards.sort)),
			db
				.select()
				.from(certifications)
				.where(eq(certifications.resumeId, resumeId))
				.orderBy(asc(certifications.sort)),
			db
				.select()
				.from(skills)
				.where(eq(skills.resumeId, resumeId))
				.orderBy(asc(skills.sort)),
			db
				.select()
				.from(languages)
				.where(eq(languages.resumeId, resumeId))
				.orderBy(asc(languages.sort)),
			db
				.select()
				.from(hobbies)
				.where(eq(hobbies.resumeId, resumeId))
				.orderBy(asc(hobbies.sort)),
			db
				.select()
				.from(volunteerings)
				.where(eq(volunteerings.resumeId, resumeId))
				.orderBy(asc(volunteerings.sort)),
			db
				.select()
				.from(profiles)
				.where(eq(profiles.resumeId, resumeId))
				.orderBy(asc(profiles.sort)),
		]);

		// Combine the data
		const fullResume = {
			...websiteData.resumes,
			educations: resumeEducations,
			experiences: resumeExperiences,
			projects: resumeProjects,
			awards: resumeAwards,
			certifications: resumeCertifications,
			skills: resumeSkills,
			languages: resumeLanguages,
			hobbies: resumeHobbies,
			volunteerings: resumeVolunteerings,
			profiles: resumeProfiles,
			references: [],
		};

		const website = {
			...websiteData.websites,
			resume: fullResume,
			websiteTemplate: websiteData.website_templates,
		};

		// Remove sensitive data before returning
		const publicWebsite = {
			website,
			resume: fullResume,
			userId: undefined,
		};

		return {
			success: true,
			data: publicWebsite,
		};
	} catch (error) {
		console.error("Error fetching public website:", error);
		return {
			success: false,
			error: "Failed to fetch website",
		};
	}
}

// Get website for a specific resume
export async function getResumeWebsite(resumeId: number) {
	try {
		const user = await requireAuth();

		// Verify the resume belongs to the user
		const [resume] = await db
			.select()
			.from(resumes)
			.where(and(eq(resumes.id, resumeId), eq(resumes.userId, user.clerkId)))
			.limit(1);

		if (!resume) {
			throw new Error("Resume not found or unauthorized");
		}

		// Get the website for this resume
		const [websiteData] = await db
			.select()
			.from(websites)
			.leftJoin(resumes, eq(websites.resumeId, resumes.id))
			.leftJoin(
				websiteTemplates,
				eq(websites.websiteTemplateId, websiteTemplates.id),
			)
			.where(
				and(eq(websites.resumeId, resumeId), eq(websites.userId, user.clerkId)),
			)
			.limit(1);

		if (!websiteData.websites) {
			return {
				success: true,
				data: null, // No website exists for this resume
			};
		}

		// Get all nested resume resources in parallel
		const [
			resumeEducations,
			resumeExperiences,
			resumeProjects,
			resumeAwards,
			resumeCertifications,
			resumeSkills,
			resumeLanguages,
			resumeReferences,
			resumeHobbies,
			resumeVolunteerings,
			resumeProfiles,
		] = await Promise.all([
			db
				.select()
				.from(educations)
				.where(eq(educations.resumeId, resumeId))
				.orderBy(asc(educations.sort)),
			db
				.select()
				.from(experiences)
				.where(eq(experiences.resumeId, resumeId))
				.orderBy(asc(experiences.sort)),
			db
				.select()
				.from(projects)
				.where(eq(projects.resumeId, resumeId))
				.orderBy(asc(projects.sort)),
			db
				.select()
				.from(awards)
				.where(eq(awards.resumeId, resumeId))
				.orderBy(asc(awards.sort)),
			db
				.select()
				.from(certifications)
				.where(eq(certifications.resumeId, resumeId))
				.orderBy(asc(certifications.sort)),
			db
				.select()
				.from(skills)
				.where(eq(skills.resumeId, resumeId))
				.orderBy(asc(skills.sort)),
			db
				.select()
				.from(languages)
				.where(eq(languages.resumeId, resumeId))
				.orderBy(asc(languages.sort)),
			db
				.select()
				.from(references)
				.where(eq(references.resumeId, resumeId))
				.orderBy(asc(references.sort)),
			db
				.select()
				.from(hobbies)
				.where(eq(hobbies.resumeId, resumeId))
				.orderBy(asc(hobbies.sort)),
			db
				.select()
				.from(volunteerings)
				.where(eq(volunteerings.resumeId, resumeId))
				.orderBy(asc(volunteerings.sort)),
			db
				.select()
				.from(profiles)
				.where(eq(profiles.resumeId, resumeId))
				.orderBy(asc(profiles.sort)),
		]);

		// Combine the data
		const fullResume = {
			...websiteData.resumes,
			educations: resumeEducations,
			experiences: resumeExperiences,
			projects: resumeProjects,
			awards: resumeAwards,
			certifications: resumeCertifications,
			skills: resumeSkills,
			languages: resumeLanguages,
			references: resumeReferences,
			hobbies: resumeHobbies,
			volunteerings: resumeVolunteerings,
			profiles: resumeProfiles,
		};

		const website = {
			...websiteData.websites,
			resume: fullResume,
			websiteTemplate: websiteData.website_templates,
		};

		return {
			success: true,
			data: website,
		};
	} catch (error) {
		console.error("Error fetching resume website:", error);
		return {
			success: false,
			error: error instanceof Error ? error.message : "Failed to fetch website",
		};
	}
}

// Delete a website
export async function deleteWebsite(websiteId: number) {
	try {
		const user = await requireAuth();

		// Verify the website belongs to the user
		const [existingWebsite] = await db
			.select()
			.from(websites)
			.where(and(eq(websites.id, websiteId), eq(websites.userId, user.clerkId)))
			.limit(1);

		if (!existingWebsite) {
			throw new Error("Website not found or unauthorized");
		}

		await db.delete(websites).where(eq(websites.id, websiteId));

		revalidatePath(`/resumes`);
		revalidatePath(`/resumes/edit/${existingWebsite.resumeId}`);
		revalidatePath(`/resumes/${existingWebsite.resumeId}/website`);

		return {
			success: true,
		};
	} catch (error) {
		console.error("Error deleting website:", error);
		return {
			success: false,
			error:
				error instanceof Error ? error.message : "Failed to delete website",
		};
	}
}

// Get all available website templates
export async function getWebsiteTemplates() {
	try {
		const templates = await db
			.select()
			.from(websiteTemplates)
			.orderBy(asc(websiteTemplates.name));

		return {
			success: true,
			data: templates,
		};
	} catch (error) {
		console.error("Error fetching website templates:", error);
		return {
			success: false,
			error: "Failed to fetch website templates",
		};
	}
}

// Generate a suggested slug from name
export async function generateSlug(
	firstName: string,
	lastName: string,
): Promise<string> {
	const fullName = `${firstName} ${lastName}`.trim();
	const baseSlug = fullName
		.toLowerCase()
		.replace(/[^a-z0-9\s-]/g, "") // Remove special characters
		.replace(/\s+/g, "-") // Replace spaces with hyphens
		.replace(/-+/g, "-") // Replace multiple hyphens with single
		.trim();

	return baseSlug;
}

// Check if slug is available
export async function checkSlugAvailability(
	slug: string,
	excludeWebsiteId?: number,
) {
	try {
		const [existingWebsite] = await db
			.select()
			.from(websites)
			.where(eq(websites.slug, slug))
			.limit(1);

		// If excluding a specific website (for updates), check if it's a different website
		const isAvailable =
			!existingWebsite ||
			(excludeWebsiteId && existingWebsite.id === excludeWebsiteId);

		return {
			success: true,
			available: isAvailable,
		};
	} catch (error) {
		console.error("Error checking slug availability:", error);
		return {
			success: false,
			error: "Failed to check slug availability",
		};
	}
}

// Toggle website public status
export async function toggleWebsitePublic(websiteId: number) {
	try {
		const user = await requireAuth();

		// Verify the website belongs to the user
		const [existingWebsite] = await db
			.select()
			.from(websites)
			.where(and(eq(websites.id, websiteId), eq(websites.userId, user.clerkId)))
			.limit(1);

		if (!existingWebsite) {
			throw new Error("Website not found or unauthorized");
		}

		// Toggle the public status
		const [updatedWebsite] = await db
			.update(websites)
			.set({
				isPublic: !existingWebsite.isPublic,
			})
			.where(eq(websites.id, websiteId))
			.returning();

		// Get the updated website with relations
		const [websiteWithRelations] = await db
			.select()
			.from(websites)
			.leftJoin(resumes, eq(websites.resumeId, resumes.id))
			.leftJoin(
				websiteTemplates,
				eq(websites.websiteTemplateId, websiteTemplates.id),
			)
			.where(eq(websites.id, websiteId))
			.limit(1);

		const finalWebsite = {
			...websiteWithRelations.websites,
			resume: websiteWithRelations.resumes,
			websiteTemplate: websiteWithRelations.website_templates,
		};

		revalidatePath(`/resumes`);
		revalidatePath(`/resumes/edit/${updatedWebsite.resumeId}`);
		revalidatePath(`/resumes/${updatedWebsite.resumeId}/website`);

		// Revalidate public website
		revalidatePath(`/cv/${updatedWebsite.slug}`);

		return {
			success: true,
			data: finalWebsite,
		};
	} catch (error) {
		console.error("Error toggling website visibility:", error);
		return {
			success: false,
			error:
				error instanceof Error
					? error.message
					: "Failed to update website visibility",
		};
	}
}
