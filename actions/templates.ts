"use server";

import { asc, eq } from "drizzle-orm";
import { db } from "@/db";
import { templates } from "@/db/schema";

// Transform template data to match frontend types
function transformTemplateForFrontend(template: any) {
	return {
		...template,
		ats_score: template.atsScore,
		created_at: template.createdAt,
		updated_at: template.updatedAt,
	};
}

export async function getTemplates() {
	try {
		const templateList = await db
			.select()
			.from(templates)
			.orderBy(asc(templates.id));

		return templateList.map(transformTemplateForFrontend);
	} catch (error) {
		console.error("Error fetching templates:", error);
		throw error;
	}
}

export async function getTemplate(id: number) {
	try {
		const [template] = await db
			.select()
			.from(templates)
			.where(eq(templates.id, id))
			.limit(1);

		if (!template) {
			throw new Error("Template not found");
		}

		return transformTemplateForFrontend(template);
	} catch (error) {
		console.error("Error fetching template:", error);
		throw error;
	}
}

// Seed default templates if they don't exist
export async function seedTemplates() {
	try {
		const templateCount = await db.select().from(templates);

		if (templateCount.length === 0) {
			const defaultTemplates = [
				{
					name: "Azurill",
					slug: "azurill",
					atsScore: "High",
					category: "Modern",
					description:
						"A clean and modern template perfect for tech professionals",
					image: "/assets/images/templates/azurill.jpg",
				},
				{
					name: "Bronzor",
					slug: "bronzor",
					atsScore: "High",
					category: "Professional",
					description:
						"A professional template suitable for corporate environments",
					image: "/assets/images/templates/bronzor.jpg",
				},
				{
					name: "Chikorita",
					slug: "chikorita",
					atsScore: "Medium",
					category: "Creative",
					description:
						"A creative template for designers and creative professionals",
					image: "/assets/images/templates/chikorita.jpg",
				},
				{
					name: "Ditto",
					slug: "ditto",
					atsScore: "High",
					category: "Minimal",
					description: "A minimal template focusing on content",
					image: "/assets/images/templates/ditto.jpg",
				},
				{
					name: "Gengar",
					slug: "gengar",
					atsScore: "Medium",
					category: "Bold",
					description: "A bold template that stands out",
					image: "/assets/images/templates/gengar.jpg",
				},
				{
					name: "Glalie",
					slug: "glalie",
					atsScore: "High",
					category: "Classic",
					description: "A classic template suitable for all industries",
					image: "/assets/images/templates/glalie.jpg",
				},
				{
					name: "Kakuna",
					slug: "kakuna",
					atsScore: "High",
					category: "Traditional",
					description: "A traditional template with a professional look",
					image: "/assets/images/templates/kakuna.jpg",
				},
				{
					name: "Leafish",
					slug: "leafish",
					atsScore: "Medium",
					category: "Natural",
					description: "A nature-inspired template with organic feel",
					image: "/assets/images/templates/leafish.jpg",
				},
				{
					name: "Nosepass",
					slug: "nosepass",
					atsScore: "High",
					category: "Structured",
					description: "A well-structured template with clear sections",
					image: "/assets/images/templates/nosepass.jpg",
				},
				{
					name: "Onyx",
					slug: "onyx",
					atsScore: "High",
					category: "Dark",
					description: "A sleek dark-themed template",
					image: "/assets/images/templates/onyx.jpg",
				},
				{
					name: "Pikachu",
					slug: "pikachu",
					atsScore: "Medium",
					category: "Bright",
					description: "A bright and energetic template",
					image: "/assets/images/templates/pikachu.jpg",
				},
				{
					name: "Rhyhorn",
					slug: "rhyhorn",
					atsScore: "High",
					category: "Strong",
					description: "A strong and bold template for impactful resumes",
					image: "/assets/images/templates/rhyhorn.jpg",
				},
			];

			await db.insert(templates).values(defaultTemplates);

			console.log("Default templates seeded successfully");
		}
	} catch (error) {
		console.error("Error seeding templates:", error);
		throw error;
	}
}
