.resume-preview {
	font-family: var(--resume-font-family);
	line-height: var(--resume-line-height);
	background: var(--resume-background);
	color: var(--resume-foreground);
}

.resume-preview h1,
.resume-preview h2,
.resume-preview h3,
.resume-preview h4,
.resume-preview h5,
.resume-preview h6 {
	color: var(--resume-primary);
}

.resume-preview p {
	color: var(--resume-foreground);
}

.resume-preview {
	height: 100%;
}

.preview-area {
	background: #f5f5f5;
	background-image: radial-gradient(circle, #e0e0e0 1px, transparent 1px);
	background-size: 20px 20px;
}

/* Dark mode preview area */
.dark .preview-area {
	background: #1a1a1a;
	background-image: radial-gradient(circle, #333333 1px, transparent 1px);
	background-size: 20px 20px;
}

.resume-preview-content {
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.1),
		0 2px 4px -1px rgba(0, 0, 0, 0.06);
	border-radius: 8px;
	overflow: hidden;
}

/* Dark mode preview content */
.dark .resume-preview-content {
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.3),
		0 2px 4px -1px rgba(0, 0, 0, 0.2);
	border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Ensure templates fill available space */
.chikorita-template,
.pikachu-template,
.gengar-template,
.glalie-template,
.bronzor-template,
.kakuna-template,
.onyx-template,
.azurill-template,
.rhyhorn-template,
.leafish-template,
.nosepass-template,
.ditto-template {
	width: 100%;
	min-height: 297mm;
	box-sizing: border-box;
}

/* Print Mode - Specific styling for print generation */
.print-mode .chikorita-template,
.print-mode .pikachu-template,
.print-mode .gengar-template,
.print-mode .glalie-template,
.print-mode .bronzor-template,
.print-mode .kakuna-template,
.print-mode .onyx-template,
.print-mode .azurill-template,
.print-mode .rhyhorn-template,
.print-mode .leafish-template,
.print-mode .nosepass-template,
.print-mode .ditto-template {
	width: 794px !important;
	min-height: 1123px !important;
	max-width: 794px !important;
	margin: 0 !important;
	padding: 0 !important;
	box-sizing: border-box !important;
	overflow: visible !important;
}

/* Print mode wrapper */
.print-mode .resume-content-wrapper {
	width: 794px !important;
	min-height: 1123px !important;
	max-width: 794px !important;
	margin: 0 !important;
	padding: 0 !important;
	box-sizing: border-box !important;
	overflow: visible !important;
	background: white !important;
}

/* PDF Export Page Break Styles */
.pdf-page-break-before {
	page-break-before: always;
	break-before: page;
}

.pdf-page-break-after {
	page-break-after: always;
	break-after: page;
}

.pdf-page-break-inside-avoid {
	page-break-inside: avoid;
	break-inside: avoid;
}

.pdf-page-break-auto {
	page-break-before: auto;
	page-break-after: auto;
	break-before: auto;
	break-after: auto;
}

/* Section-specific page break rules */
.resume-section {
	page-break-inside: avoid;
	break-inside: avoid;
}

.resume-section.can-break {
	page-break-inside: auto;
	break-inside: auto;
}

/* Experience and Education entries - Enhanced */
.experience-entry,
.education-entry,
.project-entry {
	page-break-inside: avoid;
	break-inside: avoid;
	margin-bottom: 1rem;
	orphans: 3;
	widows: 3;
}

/* Individual experience/education items */
.experience-item,
.education-item,
.project-item,
.certification-item {
	page-break-inside: avoid;
	break-inside: avoid;
	orphans: 2;
	widows: 2;
}

/* Prevent orphaned headers */
.section-header,
.section-title {
	page-break-after: avoid;
	break-after: avoid;
	orphans: 2;
	widows: 2;
}

/* Allow breaks between major sections but prefer to keep together */
.major-section-break {
	page-break-before: auto;
	break-before: auto;
	page-break-inside: avoid;
	break-inside: avoid;
}

/* Two-column layout considerations - Enhanced */
.two-column-container {
	page-break-inside: avoid;
	break-inside: avoid;
	orphans: 2;
	widows: 2;
}

/* Sidebar handling for two-column layouts */
.sidebar-content {
	page-break-inside: auto;
	break-inside: auto;
}

/* Enhanced sidebar section handling */
.sidebar-section {
	page-break-inside: avoid;
	break-inside: avoid;
	margin-bottom: 1.5rem;
}

/* Header protection - Enhanced */
.resume-header {
	page-break-after: avoid;
	break-after: avoid;
	page-break-inside: avoid;
	break-inside: avoid;
	orphans: 3;
	widows: 3;
}

/* Skills and compact sections - Enhanced */
.skills-section,
.languages-section,
.references-section {
	page-break-inside: avoid;
	break-inside: avoid;
	orphans: 2;
	widows: 2;
}

/* Multi-page layout optimization */
.chikorita-template {
	orphans: 3;
	widows: 3;
}

/* Flexible content areas that can break */
.flexible-content {
	page-break-inside: auto;
	break-inside: auto;
}

/* Content that should stay together */
.keep-together {
	page-break-inside: avoid;
	break-inside: avoid;
}

/* Smart spacing for page transitions */
.page-aware-spacing {
	margin-bottom: clamp(0.5rem, 2vw, 1rem);
}

/* Optimize vertical space usage for PDF */
@media print {
	.page-aware-spacing {
		margin-bottom: 0.75rem;
	}

	.resume-section {
		margin-bottom: 1rem;
	}

	.sidebar-section {
		margin-bottom: 1rem;
	}
}

/* Profile links grid - prevent awkward breaks */
.profiles-grid {
	page-break-inside: avoid;
	break-inside: avoid;
}

/* Contact info section */
.contact-section {
	page-break-inside: avoid;
	break-inside: avoid;
	page-break-after: avoid;
	break-after: avoid;
}

/* Print styles */
@media print {
	.preview-controls,
	.ats-score-display {
		display: none !important;
	}

	.preview-area {
		background: white !important;
		padding: 0 !important;
	}

	/* Enhanced print page breaks */
	.pdf-page-break-before {
		page-break-before: always !important;
	}

	.pdf-page-break-after {
		page-break-after: always !important;
	}

	.pdf-page-break-inside-avoid {
		page-break-inside: avoid !important;
	}

	/* Remove all margins for full page PDF */
	@page {
		margin: 0;
		size: A4;
	}

	/* Optimize for PDF export - fill entire page */
	.resume-content-wrapper {
		width: 210mm !important;
		min-height: 297mm !important;
		max-width: 210mm !important;
		margin: 0 !important;
		padding: 0 !important;
		box-sizing: border-box !important;
		overflow: hidden !important;
	}

	/* Ensure templates fill full page dimensions */
	.chikorita-template,
	.pikachu-template,
	.gengar-template,
	.glalie-template,
	.bronzor-template,
	.kakuna-template,
	.onyx-template,
	.azurill-template,
	.rhyhorn-template,
	.leafish-template,
	.nosepass-template,
	.ditto-template {
		width: 210mm !important;
		min-height: 297mm !important;
		max-width: 210mm !important;
		margin: 0 !important;
		padding: 0 !important;
		box-sizing: border-box !important;
	}

	/* Optimize text rendering for print */
	* {
		-webkit-print-color-adjust: exact !important;
		color-adjust: exact !important;
		print-color-adjust: exact !important;
	}
}

/* Additional print optimizations */
.print-mode {
	background: white !important;
}

.print-mode * {
	-webkit-print-color-adjust: exact !important;
	color-adjust: exact !important;
	print-color-adjust: exact !important;
}

/* Hide elements that shouldn't appear in print */
.no-print,
.print-mode .preview-controls,
.print-mode .ats-score-display {
	display: none !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
	.preview-controls {
		flex-direction: column;
		gap: 8px;
		align-items: stretch;
	}

	.preview-controls > div {
		justify-content: center;
	}
}
