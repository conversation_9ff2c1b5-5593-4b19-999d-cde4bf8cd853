"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { deleteWebsite, toggleWebsitePublic } from "@/actions/websites";
import { Website } from "@/db/schema";

interface WebsiteWithResume extends Website {
	resume: {
		id: number;
		title: string | null;
		firstName: string | null;
		lastName: string | null;
		jobTitle: string | null;
	} | null;
	websiteTemplate: {
		id: number;
		name: string;
		slug: string;
		preview: string;
		createdAt: string;
		updatedAt: string;
	} | null;
}

interface WebsitesPageProps {
	websites: WebsiteWithResume[];
}

export function WebsitesPage({ websites }: WebsitesPageProps) {
	const t = useTranslations("websites");
	const tCommon = useTranslations("common");
	const router = useRouter();
	const [isLoading, setIsLoading] = useState<number | null>(null);

	const handleTogglePublic = async (websiteId: number) => {
		setIsLoading(websiteId);
		try {
			await toggleWebsitePublic(websiteId);
			router.refresh();
		} catch (error) {
			console.error("Error toggling website public status:", error);
		} finally {
			setIsLoading(null);
		}
	};

	const handleDeleteWebsite = async (websiteId: number) => {
		if (!confirm(t("confirm_delete"))) return;

		setIsLoading(websiteId);
		try {
			await deleteWebsite(websiteId);
			router.refresh();
		} catch (error) {
			console.error("Error deleting website:", error);
		} finally {
			setIsLoading(null);
		}
	};

	const getWebsiteUrl = (slug: string) => {
		return `${window.location.origin}/cv/${slug}`;
	};

	console.log(websites);

	return (
		<div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800">
			<div className="max-w-7xl mx-auto px-4 py-8">
				<div className="mb-12">
					<div className="flex items-center gap-3 mb-4">
						<div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
							<Icon icon="heroicons:globe-alt" className="w-6 h-6 text-white" />
						</div>
						<h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
							{t("page_title")}
						</h1>
					</div>
					<p className="text-lg text-gray-600 dark:text-gray-400">
						{t("page_subtitle", { count: websites?.length })}
					</p>
				</div>

				{websites?.length === 0 ? (
					<div className="text-center py-16">
						<div className="mb-8">
							<div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
								<Icon
									icon="heroicons:globe-alt"
									className="w-12 h-12 text-white"
								/>
							</div>
							<h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-3">
								{t("no_websites_title")}
							</h3>
							<p className="text-gray-600 dark:text-gray-400 text-lg mb-8 max-w-md mx-auto">
								{t("no_websites_subtitle")}
							</p>
							<Button
								as={Link}
								href="/resumes"
								color="primary"
								size="lg"
								className="bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg hover:shadow-xl transition-shadow"
								startContent={<Icon icon="heroicons:plus" />}
							>
								{t("create_first_website")}
							</Button>
						</div>
					</div>
				) : (
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
						{websites?.map((website) => (
							<Card
								key={website.id}
								className="hover:shadow-xl transition-all duration-300 border-0 shadow-lg bg-white/80 backdrop-blur-sm"
							>
								<CardHeader className="pb-3">
									<div className="flex items-center gap-3 w-full">
										<div className="flex-1">
											<h3 className="font-semibold text-gray-900 dark:text-gray-100 truncate">
												{`${website.resume?.firstName || ""} ${website.resume?.lastName || ""}`}
											</h3>
											<p className="text-sm text-gray-600 dark:text-gray-400 truncate">
												{website.resume?.jobTitle || "No job title"}
											</p>
										</div>
										<Chip
											size="sm"
											variant="flat"
											color={website.isPublic ? "success" : "default"}
											className={
												website.isPublic
													? "bg-green-100 text-green-800"
													: "bg-gray-100 text-gray-600"
											}
										>
											{website.isPublic ? t("published") : t("draft")}
										</Chip>
									</div>
								</CardHeader>
								<CardBody className="pt-0">
									<div className="mb-4">
										<div className="text-sm text-gray-500 dark:text-gray-400 mb-1">
											{t("template")}
										</div>
										<div className="font-medium text-gray-900 dark:text-gray-100">
											{website.websiteTemplate?.name || "Unknown Template"}
										</div>
									</div>

									{website.isPublic && (
										<div className="mb-6">
											<div className="text-sm text-gray-500 dark:text-gray-400 mb-2">
												{t("website_url")}
											</div>
											<Link
												href={getWebsiteUrl(website.slug)}
												target="_blank"
												className="text-sm text-blue-600 hover:text-blue-800 break-all bg-blue-50 px-2 py-1 rounded"
											>
												{getWebsiteUrl(website.slug)}
											</Link>
										</div>
									)}

									<div className="flex gap-2">
										<Button
											as={Link}
											href={`/resumes/${website.resume?.id}/website`}
											size="sm"
											variant="flat"
											className="flex-1 bg-blue-50 hover:bg-blue-100 text-blue-700"
											startContent={<Icon icon="heroicons:pencil" />}
										>
											{tCommon("edit")}
										</Button>
										<Button
											size="sm"
											variant="flat"
											color={website.isPublic ? "warning" : "success"}
											className={
												website.isPublic
													? "bg-orange-50 hover:bg-orange-100 text-orange-700"
													: "bg-green-50 hover:bg-green-100 text-green-700"
											}
											startContent={
												<Icon
													icon={
														website.isPublic
															? "heroicons:eye-slash"
															: "heroicons:eye"
													}
												/>
											}
											isLoading={isLoading === website.id}
											onPress={() => handleTogglePublic(website.id)}
										>
											{website.isPublic ? t("unpublish") : t("publish")}
										</Button>
										<Button
											size="sm"
											variant="flat"
											color="danger"
											className="bg-red-50 hover:bg-red-100 text-red-700"
											startContent={<Icon icon="heroicons:trash" />}
											isLoading={isLoading === website.id}
											onPress={() => handleDeleteWebsite(website.id)}
										>
											{tCommon("delete")}
										</Button>
									</div>
								</CardBody>
							</Card>
						))}
					</div>
				)}
			</div>
		</div>
	);
}
