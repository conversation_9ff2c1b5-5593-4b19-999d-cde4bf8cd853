import { useCallback, useEffect, useState } from "react";

export interface BackgroundPattern {
	id: string;
	name: string;
	cssClass: string;
	darkCssClass?: string;
	description: string;
	category: "minimal" | "textured" | "gradient";
	preview: string;
}

export const BACKGROUND_PATTERNS: BackgroundPattern[] = [
	{
		id: "none",
		name: "Clean",
		cssClass: "bg-pattern-none",
		description: "Clean white background for maximum readability",
		category: "minimal",
		preview: "#ffffff",
	},
	{
		id: "dots",
		name: "Subtle Dots",
		cssClass: "bg-pattern-dots",
		darkCssClass: "bg-pattern-dots-dark",
		description: "Professional dot pattern with minimal opacity",
		category: "textured",
		preview: "radial-gradient(circle at 2px 2px, #e5e5e5 1px, transparent 0)",
	},
	{
		id: "grid",
		name: "Light Grid",
		cssClass: "bg-pattern-grid",
		darkCssClass: "bg-pattern-grid-dark",
		description: "Minimal grid lines for structured appearance",
		category: "textured",
		preview:
			"linear-gradient(#e5e5e5 1px, transparent 1px), linear-gradient(90deg, #e5e5e5 1px, transparent 1px)",
	},
	{
		id: "diagonal",
		name: "Diagonal Lines",
		cssClass: "bg-pattern-diagonal",
		darkCssClass: "bg-pattern-diagonal-dark",
		description: "Elegant diagonal texture with subtle opacity",
		category: "textured",
		preview:
			"linear-gradient(45deg, transparent 24%, #e5e5e5 25%, #e5e5e5 26%, transparent 27%)",
	},
	{
		id: "paper",
		name: "Paper Grain",
		cssClass: "bg-pattern-paper",
		description: "Subtle noise pattern mimicking high-quality paper",
		category: "textured",
		preview: "#fafafa",
	},
	{
		id: "gradient",
		name: "Soft Gradient",
		cssClass: "bg-pattern-gradient",
		description: "Very subtle radial gradient for depth",
		category: "gradient",
		preview: "radial-gradient(ellipse at center, #ffffff 0%, #fafafa 100%)",
	},
];

interface UseBackgroundOptions {
	defaultPattern?: string;
	isDarkTheme?: boolean;
	persistToStorage?: boolean;
	storageKey?: string;
}

export const useBackground = (options: UseBackgroundOptions = {}) => {
	const {
		defaultPattern = "none",
		isDarkTheme = false,
		persistToStorage = true,
		storageKey = "website-background-pattern",
	} = options;

	const [currentPattern, setCurrentPattern] = useState<string>(defaultPattern);
	const [isLoading, setIsLoading] = useState(false);

	// Load saved pattern from localStorage on mount
	useEffect(() => {
		if (persistToStorage && typeof window !== "undefined") {
			const savedPattern = localStorage.getItem(storageKey);
			if (
				savedPattern &&
				BACKGROUND_PATTERNS.find((p) => p.id === savedPattern)
			) {
				setCurrentPattern(savedPattern);
			}
		}
	}, [persistToStorage, storageKey]);

	// Get the current pattern object
	const getCurrentPattern = useCallback((): BackgroundPattern => {
		return (
			BACKGROUND_PATTERNS.find((p) => p.id === currentPattern) ||
			BACKGROUND_PATTERNS[0]
		);
	}, [currentPattern]);

	// Get the CSS class for the current pattern
	const getPatternClass = useCallback((): string => {
		const pattern = getCurrentPattern();
		const baseClass = "website-background bg-pattern-transition";
		const patternClass =
			isDarkTheme && pattern.darkCssClass
				? pattern.darkCssClass
				: pattern.cssClass;

		return `${baseClass} ${patternClass}`;
	}, [getCurrentPattern, isDarkTheme]);

	// Update the current pattern
	const updatePattern = useCallback(
		async (patternId: string) => {
			const pattern = BACKGROUND_PATTERNS.find((p) => p.id === patternId);
			if (!pattern) {
				console.warn(`Background pattern '${patternId}' not found`);
				return;
			}

			setIsLoading(true);

			try {
				setCurrentPattern(patternId);

				// Save to localStorage if persistence is enabled
				if (persistToStorage && typeof window !== "undefined") {
					localStorage.setItem(storageKey, patternId);
				}
			} catch (error) {
				console.error("Failed to update background pattern:", error);
			} finally {
				setIsLoading(false);
			}
		},
		[persistToStorage, storageKey],
	);

	// Get patterns by category
	const getPatternsByCategory = useCallback(
		(category: BackgroundPattern["category"]) => {
			return BACKGROUND_PATTERNS.filter((p) => p.category === category);
		},
		[],
	);

	// Check if a pattern is available for dark theme
	const isPatternDarkCompatible = useCallback(
		(patternId: string): boolean => {
			const pattern = BACKGROUND_PATTERNS.find((p) => p.id === patternId);
			return !!(pattern && (pattern.darkCssClass || !isDarkTheme));
		},
		[isDarkTheme],
	);

	// Get recommended patterns based on template type
	const getRecommendedPatterns = useCallback(
		(templateType?: string): BackgroundPattern[] => {
			switch (templateType) {
				case "executive":
					return BACKGROUND_PATTERNS.filter((p) =>
						["none", "gradient", "paper"].includes(p.id),
					);
				case "creative":
					return BACKGROUND_PATTERNS.filter((p) =>
						["dots", "diagonal", "gradient"].includes(p.id),
					);
				case "academic":
					return BACKGROUND_PATTERNS.filter((p) =>
						["none", "grid", "paper"].includes(p.id),
					);
				case "tech":
					return BACKGROUND_PATTERNS.filter((p) =>
						["none", "dots", "grid"].includes(p.id),
					);
				case "minimalist":
					return BACKGROUND_PATTERNS.filter((p) =>
						["none", "gradient"].includes(p.id),
					);
				default:
					return BACKGROUND_PATTERNS;
			}
		},
		[],
	);

	return {
		// State
		currentPattern,
		isLoading,
		isDarkTheme,

		// Pattern data
		patterns: BACKGROUND_PATTERNS,
		currentPatternData: getCurrentPattern(),

		// Actions
		updatePattern,

		// Computed values
		getPatternClass,
		getPatternsByCategory,
		getRecommendedPatterns,
		isPatternDarkCompatible,

		// Utilities
		resetToDefault: () => updatePattern(defaultPattern),

		// Pattern validation
		isValidPattern: (patternId: string) =>
			BACKGROUND_PATTERNS.some((p) => p.id === patternId),
	};
};
