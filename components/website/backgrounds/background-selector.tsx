"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Divider, Toolt<PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import {
	BACKGROUND_PATTERNS,
	BackgroundPattern,
	useBackground,
} from "./use-background";

interface BackgroundSelectorProps {
	currentPattern: string;
	onPatternChange: (patternId: string) => void;
	templateType?: string;
	isDarkTheme?: boolean;
	className?: string;
}

export function BackgroundSelector({
	currentPattern,
	onPatternChange,
	templateType,
	isDarkTheme = false,
	className = "",
}: BackgroundSelectorProps) {
	const t = useTranslations("website_builder");
	const [selectedCategory, setSelectedCategory] = useState<string>("all");
	const { getRecommendedPatterns, isPatternDarkCompatible } = useBackground({
		defaultPattern: currentPattern,
		isDarkTheme,
	});

	// Get patterns to display based on category filter
	const getFilteredPatterns = (): BackgroundPattern[] => {
		let patterns = BACKGROUND_PATTERNS;

		// Filter by template recommendations if provided
		if (templateType && selectedCategory === "recommended") {
			patterns = getRecommendedPatterns(templateType);
		} else if (selectedCategory !== "all") {
			patterns = patterns.filter((p) => p.category === selectedCategory);
		}

		// Filter out dark-incompatible patterns if in dark theme
		return patterns.filter((p) => isPatternDarkCompatible(p.id));
	};

	const filteredPatterns = getFilteredPatterns();

	const categories = [
		{
			id: "all",
			name: t("background.categories.all"),
			icon: "heroicons:squares-2x2",
		},
		...(templateType
			? [
					{
						id: "recommended",
						name: t("background.categories.recommended"),
						icon: "heroicons:star",
					},
				]
			: []),
		{
			id: "minimal",
			name: t("background.categories.minimal"),
			icon: "heroicons:minus-circle",
		},
		{
			id: "textured",
			name: t("background.categories.textured"),
			icon: "heroicons:swatch",
		},
		{
			id: "gradient",
			name: t("background.categories.gradient"),
			icon: "heroicons:paint-brush",
		},
	];

	const renderPatternPreview = (pattern: BackgroundPattern) => {
		const previewStyle: React.CSSProperties = {
			background: pattern.preview,
			backgroundSize: pattern.id === "dots" ? "8px 8px" : "12px 12px",
		};

		return (
			<div
				className="w-full h-16 rounded-lg border-2 border-gray-200 dark:border-gray-600 transition-all duration-200"
				style={previewStyle}
			/>
		);
	};

	return (
		<div className={`space-y-4 ${className}`}>
			{/* Header */}
			<div className="flex items-center gap-2">
				<Icon icon="heroicons:swatch" className="w-5 h-5" />
				<h3 className="text-lg font-semibold">{t("background.title")}</h3>
			</div>

			<p className="text-sm text-gray-600 dark:text-gray-400">
				{t("background.description")}
			</p>

			{/* Category Filter */}
			<div className="flex flex-wrap gap-2">
				{categories.map((category) => (
					<Button
						key={category.id}
						size="sm"
						variant={selectedCategory === category.id ? "solid" : "flat"}
						color={selectedCategory === category.id ? "primary" : "default"}
						startContent={<Icon icon={category.icon} className="w-4 h-4" />}
						onPress={() => setSelectedCategory(category.id)}
						className="text-xs"
					>
						{category.name}
					</Button>
				))}
			</div>

			<Divider />

			{/* Pattern Grid */}
			<div className="grid grid-cols-2 md:grid-cols-3 gap-3">
				{filteredPatterns.map((pattern) => {
					const isSelected = currentPattern === pattern.id;
					const isRecommended =
						templateType &&
						getRecommendedPatterns(templateType).some(
							(p) => p.id === pattern.id,
						);

					return (
						<Tooltip
							key={pattern.id}
							content={pattern.description}
							placement="top"
							className="max-w-xs"
						>
							<Card
								isPressable
								isHoverable
								className={`
									relative cursor-pointer transition-all duration-200 border-2
									${
										isSelected
											? "border-primary bg-primary-50 dark:bg-primary-950/20"
											: "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
									}
								`}
								onPress={() => onPatternChange(pattern.id)}
							>
								<CardBody className="p-3 space-y-3">
									{/* Pattern Preview */}
									{renderPatternPreview(pattern)}

									{/* Pattern Info */}
									<div className="space-y-1">
										<div className="flex items-center justify-between">
											<span className="text-sm font-medium text-gray-900 dark:text-gray-100">
												{pattern.name}
											</span>
											{isRecommended && (
												<Chip
													size="sm"
													variant="flat"
													color="warning"
													className="text-xs"
													startContent={
														<Icon icon="heroicons:star" className="w-3 h-3" />
													}
												>
													{t("background.recommended")}
												</Chip>
											)}
										</div>

										{/* Category Badge */}
										<Chip
											size="sm"
											variant="flat"
											color="default"
											className="text-xs"
										>
											{pattern.category}
										</Chip>
									</div>

									{/* Selection Indicator */}
									{isSelected && (
										<div className="absolute top-2 right-2">
											<div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
												<Icon
													icon="heroicons:check"
													className="w-3 h-3 text-white"
												/>
											</div>
										</div>
									)}
								</CardBody>
							</Card>
						</Tooltip>
					);
				})}
			</div>

			{/* No patterns message */}
			{filteredPatterns.length === 0 && (
				<div className="text-center py-8">
					<Icon
						icon="heroicons:face-frown"
						className="w-12 h-12 text-gray-400 mx-auto mb-2"
					/>
					<p className="text-gray-500 dark:text-gray-400">
						{t("background.no_patterns")}
					</p>
					<Button
						size="sm"
						variant="flat"
						onPress={() => setSelectedCategory("all")}
						className="mt-2"
					>
						{t("background.show_all")}
					</Button>
				</div>
			)}

			{/* Pattern Info */}
			{currentPattern && (
				<Card className="bg-gray-50 dark:bg-gray-800/50">
					<CardBody className="p-4">
						<div className="flex items-start gap-3">
							<Icon
								icon="heroicons:information-circle"
								className="w-5 h-5 mt-0.5 text-blue-500"
							/>
							<div className="flex-1 space-y-1">
								<h4 className="font-medium text-sm">
									{
										BACKGROUND_PATTERNS.find((p) => p.id === currentPattern)
											?.name
									}
								</h4>
								<p className="text-xs text-gray-600 dark:text-gray-400">
									{
										BACKGROUND_PATTERNS.find((p) => p.id === currentPattern)
											?.description
									}
								</p>
							</div>
						</div>
					</CardBody>
				</Card>
			)}

			{/* Quick Actions */}
			<div className="flex gap-2 pt-2">
				<Button
					size="sm"
					variant="flat"
					startContent={
						<Icon icon="heroicons:arrow-path" className="w-4 h-4" />
					}
					onPress={() => onPatternChange("none")}
					isDisabled={currentPattern === "none"}
				>
					{t("background.reset")}
				</Button>

				{templateType && (
					<Button
						size="sm"
						variant="flat"
						color="warning"
						startContent={<Icon icon="heroicons:star" className="w-4 h-4" />}
						onPress={() => {
							const recommended = getRecommendedPatterns(templateType);
							if (recommended.length > 0) {
								onPatternChange(recommended[0].id);
							}
						}}
					>
						{t("background.use_recommended")}
					</Button>
				)}
			</div>
		</div>
	);
}
