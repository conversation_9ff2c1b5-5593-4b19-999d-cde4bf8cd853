/* Professional Background Patterns for Website Templates */

/* Base container for background application */
.website-background {
	position: relative;
	min-height: 100vh;
	transition: all 0.3s ease-in-out;
	contain: layout style paint;
	will-change: background-image;
}

/* Pattern: None - Clean white background */
.bg-pattern-none {
	background: #ffffff;
}

/* Pattern: Subtle Dots - Professional dot pattern */
.bg-pattern-dots {
	background-color: #ffffff;
	background-image: radial-gradient(
		circle at 1px 1px,
		rgba(0, 0, 0, 0.03) 1px,
		transparent 0
	);
	background-size: 24px 24px;
}

/* Pattern: Light Grid - Minimal grid lines */
.bg-pattern-grid {
	background-color: #ffffff;
	background-image:
		linear-gradient(rgba(0, 0, 0, 0.025) 1px, transparent 1px),
		linear-gradient(90deg, rgba(0, 0, 0, 0.025) 1px, transparent 1px);
	background-size: 24px 24px;
}

/* Pattern: Diagonal Lines - Elegant diagonal texture */
.bg-pattern-diagonal {
	background-color: #ffffff;
	background-image: linear-gradient(
		45deg,
		transparent 24%,
		rgba(0, 0, 0, 0.02) 25%,
		rgba(0, 0, 0, 0.02) 26%,
		transparent 27%,
		transparent 74%,
		rgba(0, 0, 0, 0.02) 75%,
		rgba(0, 0, 0, 0.02) 76%,
		transparent 77%
	);
	background-size: 32px 32px;
}

/* Pattern: Paper Grain - Subtle noise pattern */
.bg-pattern-paper {
	background-color: #ffffff;
	background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='1' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.02'/%3E%3C/svg%3E");
}

/* Pattern: Soft Gradient - Very subtle radial gradient */
.bg-pattern-gradient {
	background: radial-gradient(
		ellipse at center,
		#ffffff 0%,
		#fafafa 50%,
		#f5f5f5 100%
	);
}

/* Dark theme variants for templates that use dark backgrounds */
.bg-pattern-dots-dark {
	background-color: #1a1a1a;
	background-image: radial-gradient(
		circle at 1px 1px,
		rgba(255, 255, 255, 0.05) 1px,
		transparent 0
	);
	background-size: 24px 24px;
}

.bg-pattern-grid-dark {
	background-color: #1a1a1a;
	background-image:
		linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
		linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
	background-size: 24px 24px;
}

.bg-pattern-diagonal-dark {
	background-color: #1a1a1a;
	background-image: linear-gradient(
		45deg,
		transparent 24%,
		rgba(255, 255, 255, 0.03) 25%,
		rgba(255, 255, 255, 0.03) 26%,
		transparent 27%,
		transparent 74%,
		rgba(255, 255, 255, 0.03) 75%,
		rgba(255, 255, 255, 0.03) 76%,
		transparent 77%
	);
	background-size: 32px 32px;
}

/* Animation for smooth pattern transitions */
.bg-pattern-transition {
	transition:
		background-image 0.4s ease-in-out,
		background-color 0.4s ease-in-out;
}

/* Responsive adjustments for mobile devices */
@media (max-width: 768px) {
	.bg-pattern-dots,
	.bg-pattern-dots-dark {
		background-size: 20px 20px;
	}

	.bg-pattern-grid,
	.bg-pattern-grid-dark {
		background-size: 20px 20px;
	}

	.bg-pattern-diagonal,
	.bg-pattern-diagonal-dark {
		background-size: 28px 28px;
	}
}

/* Print styles - ensure clean backgrounds for PDF export */
@media print {
	.website-background,
	.bg-pattern-dots,
	.bg-pattern-grid,
	.bg-pattern-diagonal,
	.bg-pattern-paper,
	.bg-pattern-gradient,
	.bg-pattern-dots-dark,
	.bg-pattern-grid-dark,
	.bg-pattern-diagonal-dark {
		background: white !important;
		background-image: none !important;
	}
}

/* Accessibility - respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
	.bg-pattern-transition,
	.website-background {
		transition: none;
	}
}

/* High contrast mode support */
@media (prefers-contrast: high) {
	.bg-pattern-dots,
	.bg-pattern-grid,
	.bg-pattern-diagonal {
		background-image: none;
	}
}
