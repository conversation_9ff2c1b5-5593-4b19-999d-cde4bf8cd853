"use client";

import { useRouter } from "@bprogress/next/app";
import { But<PERSON> } from "@heroui/button";
import { Input } from "@heroui/input";
import { Card, CardBody, CardHeader } from "@heroui/react";
import { Switch } from "@heroui/switch";
import { useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import {
	checkSlugAvailability,
	createWebsite,
	generateSlug,
	toggleWebsitePublic,
	updateWebsite,
} from "@/actions/websites";
import { WebsiteTemplateSelector } from "./templates/template-registry";
import { WebsitePreview } from "./website-preview";

interface WebsiteBuilderProps {
	resume: any;
	existingWebsite?: any;
	templates: any[];
}

export const WebsiteBuilder: React.FC<WebsiteBuilderProps> = ({
	resume,
	existingWebsite,
	templates,
}) => {
	const router = useRouter();
	const t = useTranslations("website");
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [success, setSuccess] = useState<string | null>(null);

	// Form state
	const [selectedTemplate, setSelectedTemplate] = useState<number>(
		existingWebsite?.websiteTemplateId || templates[0]?.id || 1,
	);
	const [slug, setSlug] = useState(existingWebsite?.slug || "");
	const [isPublic, setIsPublic] = useState(existingWebsite?.isPublic || false);

	// Validation state
	const [slugError, setSlugError] = useState<string | null>(null);
	const [isCheckingSlug, setIsCheckingSlug] = useState(false);

	// Generate initial slug from resume data
	useEffect(() => {
		if (!existingWebsite && resume && !slug) {
			const generateInitialSlug = async () => {
				const generatedSlug = await generateSlug(
					resume.firstName || "",
					resume.lastName || "",
				);
				setSlug(generatedSlug);
			};
			generateInitialSlug();
		}
	}, [resume, existingWebsite, slug]);

	// Validate slug availability
	const validateSlug = async (slugValue: string) => {
		if (!slugValue) {
			setSlugError("URL slug is required");
			return false;
		}

		if (slugValue.length < 3) {
			setSlugError(t("validation.slug_min_length"));
			return false;
		}

		if (!/^[a-z0-9-]+$/.test(slugValue)) {
			setSlugError(t("validation.slug_format"));
			return false;
		}

		setIsCheckingSlug(true);
		try {
			const result = await checkSlugAvailability(
				slugValue,
				existingWebsite?.id,
			);
			if (result.success && !result.available) {
				setSlugError(t("validation.slug_taken"));
				return false;
			}
			setSlugError(null);
			return true;
		} catch (error) {
			setSlugError(t("validation.slug_error"));
			return false;
		} finally {
			setIsCheckingSlug(false);
		}
	};

	// Handle slug change with debouncing
	useEffect(() => {
		const timeoutId = setTimeout(() => {
			if (slug) {
				validateSlug(slug);
			}
		}, 500);

		return () => clearTimeout(timeoutId);
	}, [slug, existingWebsite?.id]);

	const handleSave = async () => {
		setIsLoading(true);
		setError(null);
		setSuccess(null);

		try {
			// Validate slug first
			const isSlugValid = await validateSlug(slug);
			if (!isSlugValid) {
				setIsLoading(false);
				return;
			}

			const websiteData = {
				slug,
			};

			let result;

			if (existingWebsite) {
				// Update existing website
				result = await updateWebsite(existingWebsite.id, {
					...websiteData,
					websiteTemplateId: selectedTemplate,
				});
			} else {
				// Create new website
				result = await createWebsite(resume.id, selectedTemplate, websiteData);
			}

			if (result.success) {
				setSuccess(
					existingWebsite
						? t("messages.updated_success")
						: t("messages.created_success"),
				);
				// Refresh the page to get updated data
				router.refresh();
			} else {
				setError(result.error || t("messages.create_failed"));
			}
		} catch (error) {
			setError(t("messages.unexpected_error"));
			console.error("Save website error:", error);
		} finally {
			setIsLoading(false);
		}
	};

	const handleTogglePublic = async () => {
		if (!existingWebsite) return;

		setIsLoading(true);
		try {
			const result = await toggleWebsitePublic(existingWebsite.id);
			if (result.success) {
				setIsPublic(!isPublic);
				setSuccess(
					!isPublic ? "Website published successfully!" : "Website unpublished",
				);
				router.refresh();
			} else {
				setError(result.error || "Failed to toggle website visibility");
			}
		} catch (error) {
			setError("An unexpected error occurred");
		} finally {
			setIsLoading(false);
		}
	};

	const selectedTemplateData = templates.find((t) => t.id === selectedTemplate);
	const websitePrefix = `${process.env.NEXT_PUBLIC_BASE_URL || "https://quickcv.com"}/cv/`;
	const websiteUrl = `${websitePrefix}${slug}`;

	return (
		<div className="grid lg:grid-cols-12 gap-8 min-h-screen">
			{/* Settings Panel */}
			<div className="lg:col-span-4 space-y-6 max-h-screen overflow-y-auto">
				{/* Status and Actions */}
				{existingWebsite && (
					<Card>
						<CardHeader>
							<h3 className="text-lg font-semibold">
								{t("builder.website_status")}
							</h3>
						</CardHeader>
						<CardBody className="space-y-4">
							<div className="flex items-center justify-between">
								<span className="text-sm text-gray-600">
									Status:{" "}
									{isPublic ? (
										<span className="text-green-600 font-medium">
											Published
										</span>
									) : (
										<span className="text-orange-600 font-medium">Draft</span>
									)}
								</span>
								<Switch
									isSelected={isPublic}
									onValueChange={handleTogglePublic}
									isDisabled={isLoading}
									size="sm"
								>
									{isPublic ? "Public" : "Private"}
								</Switch>
							</div>

							{isPublic && (
								<div className="p-3 bg-blue-50 rounded-lg">
									<p className="text-sm text-blue-700 mb-2">
										Your website is live at:
									</p>
									<a
										href={websiteUrl}
										target="_blank"
										rel="noopener noreferrer"
										className="text-sm text-blue-600 hover:text-blue-800 break-all"
									>
										{websiteUrl}
									</a>
								</div>
							)}
						</CardBody>
					</Card>
				)}

				{/* Template Selection */}
				<Card>
					<CardHeader>
						<h3 className="text-lg font-semibold">
							{t("builder.choose_template")}
						</h3>
					</CardHeader>
					<CardBody className="h-full">
						<WebsiteTemplateSelector
							selectedTemplate={selectedTemplateData?.slug}
							websiteTemplates={templates}
							onTemplateSelect={(templateSlug) => {
								console.log(templateSlug);
								const template = templates.find((t) => t.slug === templateSlug);
								if (template) {
									setSelectedTemplate(template.id);
								}
							}}
						/>
					</CardBody>
				</Card>

				{/* Website Settings */}
				<Card>
					<CardHeader>
						<h3 className="text-lg font-semibold">
							{t("builder.website_settings")}
						</h3>
					</CardHeader>
					<CardBody className="space-y-4">
						<div>
							<Input
								label="Website URL"
								placeholder="your-name-developer"
								value={slug}
								onChange={(e) =>
									setSlug(
										e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, ""),
									)
								}
								startContent={
									<span className="text-sm text-gray-500">{websitePrefix}</span>
								}
								errorMessage={slugError}
								isInvalid={!!slugError}
								isDisabled={isLoading}
								description="This will be your public website URL"
							/>
							{isCheckingSlug && (
								<p className="text-sm text-gray-500 mt-1">
									Checking availability...
								</p>
							)}
						</div>
					</CardBody>
				</Card>

				{/* Actions */}
				<div className="space-y-3">
					<Button
						color="primary"
						onClick={handleSave}
						isLoading={isLoading}
						isDisabled={!!slugError || isCheckingSlug}
						className="w-full"
					>
						{existingWebsite ? "Update Website" : "Create Website"}
					</Button>

					{existingWebsite && !isPublic && (
						<Button
							color="success"
							variant="bordered"
							onClick={handleTogglePublic}
							isLoading={isLoading}
							className="w-full"
						>
							Publish Website
						</Button>
					)}
				</div>

				{/* Messages */}
				{error && (
					<div className="p-3 bg-red-50 border border-red-200 rounded-lg">
						<p className="text-sm text-red-700">{error}</p>
					</div>
				)}

				{success && (
					<div className="p-3 bg-green-50 border border-green-200 rounded-lg">
						<p className="text-sm text-green-700">{success}</p>
					</div>
				)}
			</div>

			{/* Preview Panel */}
			<div className="lg:col-span-8 h-screen">
				<Card className="h-full">
					<CardHeader>
						<div className="flex items-center justify-between w-full">
							<h3 className="text-lg font-semibold">Preview</h3>
							{selectedTemplateData && (
								<span className="text-sm text-gray-600">
									Template: {selectedTemplateData.name}
								</span>
							)}
						</div>
					</CardHeader>
					<CardBody className="p-0 h-full">
						<div className="h-full border rounded-lg overflow-hidden bg-white">
							<div className="w-full h-full overflow-auto">
								<WebsitePreview
									resume={resume}
									website={{
										id: existingWebsite?.id || 0,
										slug: slug || "preview",
										isPublic: isPublic,
										analytics: false,
										userId: resume.userId,
										resumeId: resume.id,
										websiteTemplateId: selectedTemplate,
										createdAt: new Date().toISOString(),
										updatedAt: new Date().toISOString(),
									}}
									templateSlug={selectedTemplateData?.slug || "portfolio"}
									className="w-full min-h-full"
								/>
							</div>
						</div>
					</CardBody>
				</Card>
			</div>
		</div>
	);
};
