"use client";

import Image from "next/image";
import React from "react";
import {
	formatDateRange,
	formatLocation,
	getFullName,
} from "@/components/resume/templates/base-components";
import {
	WebsiteContact,
	WebsiteEducation,
	WebsiteSection,
	WebsiteTemplateProps,
} from "./base-components";

export const ExecutiveTemplate: React.FC<WebsiteTemplateProps> = ({
	resume,
	website,
	className = "",
}) => {
	const fullName = getFullName(resume.firstName, resume.lastName);
	const displayTitle = resume.jobTitle;
	const displayBio = resume.bio;
	const location = formatLocation(resume.city || "", resume.country || "");

	// Filter and sort experiences for executives (focus on leadership roles)
	const experiences =
		resume.experiences
			?.filter((exp) => exp.title && exp.company)
			.sort((a, b) => (b.sort || 0) - (a.sort || 0)) || [];

	// Filter significant achievements from awards and certifications
	const achievements = [
		...(resume.awards || []),
		...(resume.certifications || []),
	]
		.filter((item) => item.title)
		.sort((a, b) => {
			// Sort by date if available, otherwise by sort order
			const aDate = new Date(a.dateReceived || a.dateReceived || 0);
			const bDate = new Date(b.dateReceived || b.dateReceived || 0);
			return bDate.getTime() - aDate.getTime();
		})
		.slice(0, 6); // Show only top 6 achievements

	// Education sorted by completion date
	const educations =
		resume.educations
			?.filter((edu) => edu.institution && edu.degree)
			.sort((a, b) => {
				const aDate = new Date(a.endDate || a.endDate || 0);
				const bDate = new Date(b.endDate || b.endDate || 0);
				return bDate.getTime() - aDate.getTime();
			}) || [];

	// Skills grouped and filtered for executive level
	const executiveSkills =
		resume.skills
			?.filter((skill) => skill.name && (skill.proficiency || 0) >= 70)
			.reduce(
				(acc, skill) => {
					const category = skill.category || "Leadership";
					if (!acc[category]) acc[category] = [];
					acc[category].push(skill);
					return acc;
				},
				{} as Record<string, typeof resume.skills>,
			) || {};

	const navSections = [
		{ id: "about", label: "About" },
		{ id: "contact", label: "Contact" },
		...(experiences.length > 0
			? [{ id: "experience", label: "Experience" }]
			: []),
		...(achievements.length > 0
			? [{ id: "achievements", label: "Achievements" }]
			: []),
		...(Object.keys(executiveSkills).length > 0
			? [{ id: "expertise", label: "Expertise" }]
			: []),
		...(educations.length > 0 ? [{ id: "education", label: "Education" }] : []),
	];

	return (
		<div
			className={`executive-template bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen ${className}`}
		>
			{/* Navigation Bar */}
			<nav className="bg-white/90 backdrop-blur-sm shadow-sm border-b sticky top-0 z-50">
				<div className="max-w-6xl mx-auto px-6 py-4">
					<div className="flex justify-between items-center">
						<div className="font-bold text-xl text-slate-800">{fullName}</div>
						<div className="hidden md:flex space-x-8">
							{navSections.map((section) => (
								<a
									key={section.id}
									href={`#${section.id}`}
									className="text-slate-600 hover:text-slate-900 transition-colors font-medium"
								>
									{section.label}
								</a>
							))}
						</div>
					</div>
				</div>
			</nav>

			{/* Executive Hero Section */}
			<section id="about" className="py-20 px-6">
				<div className="max-w-4xl mx-auto text-center">
					{resume.showPhoto && resume.photo && (
						<div className="mb-8">
							<Image
								alt={fullName}
								className="w-48 h-48 rounded-full object-cover mx-auto shadow-2xl ring-8 ring-white"
								height={192}
								src={resume.photo}
								width={192}
								priority
							/>
						</div>
					)}

					<h1 className="text-5xl md:text-6xl font-bold text-slate-900 mb-4 tracking-tight">
						{fullName}
					</h1>

					{displayTitle && (
						<p className="text-2xl md:text-3xl text-slate-700 mb-6 font-light">
							{displayTitle}
						</p>
					)}

					{location && (
						<p className="text-lg text-slate-600 mb-8 flex items-center justify-center gap-2">
							<span className="text-xl">📍</span>
							{location}
						</p>
					)}

					{displayBio && (
						<div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-slate-200">
							<div
								dangerouslySetInnerHTML={{ __html: displayBio }}
								className="text-lg text-slate-700 leading-relaxed max-w-3xl mx-auto"
							/>
						</div>
					)}
				</div>
			</section>

			{/* Professional Experience */}
			{experiences.length > 0 && (
				<WebsiteSection
					id="experience"
					title="Professional Experience"
					className="max-w-6xl mx-auto px-6"
				>
					<div className="space-y-8">
						{experiences.map((experience) => {
							const location = formatLocation(
								experience.city || "",
								experience.country || "",
							);
							const dateRange = formatDateRange(
								experience.startDate,
								experience.endDate,
								experience.isCurrent,
							);

							return (
								<div
									key={experience.id}
									className="bg-white rounded-xl p-8 shadow-lg border border-slate-200 hover:shadow-xl transition-shadow"
								>
									<div className="flex flex-col lg:flex-row lg:justify-between lg:items-start mb-6">
										<div className="flex-1">
											<h3 className="text-2xl font-bold text-slate-900 mb-2">
												{experience.title}
											</h3>
											<p className="text-xl text-blue-700 font-semibold mb-2">
												{experience.company}
											</p>
											{location && (
												<p className="text-slate-600 flex items-center gap-2">
													<span>📍</span>
													{location}
												</p>
											)}
										</div>
										<div className="mt-4 lg:mt-0 lg:text-right">
											<div className="bg-slate-100 px-4 py-2 rounded-lg">
												<p className="font-semibold text-slate-700">
													{dateRange}
												</p>
											</div>
										</div>
									</div>
									{experience.description && (
										<div
											dangerouslySetInnerHTML={{
												__html: experience.description,
											}}
											className="text-slate-700 leading-relaxed prose prose-slate max-w-none"
										/>
									)}
								</div>
							);
						})}
					</div>
				</WebsiteSection>
			)}

			{/* Key Achievements */}
			{achievements.length > 0 && (
				<WebsiteSection
					id="achievements"
					title="Key Achievements"
					className="max-w-6xl mx-auto px-6 bg-slate-50/50 py-16 rounded-3xl"
				>
					<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
						{achievements.map((achievement) => {
							const date = achievement.dateReceived || achievement.dateReceived;
							const formattedDate = date
								? new Date(date).toLocaleDateString(undefined, {
										year: "numeric",
										month: "long",
									})
								: null;

							return (
								<div
									key={achievement.id}
									className="bg-white rounded-xl p-6 shadow-md border border-slate-200 hover:shadow-lg transition-shadow"
								>
									<div className="flex items-start gap-3 mb-4">
										<div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-amber-600 rounded-full flex items-center justify-center flex-shrink-0">
											<span className="text-white text-lg">🏆</span>
										</div>
										<div className="flex-1 min-w-0">
											<h4 className="font-bold text-slate-900 text-lg leading-tight">
												{achievement.title}
											</h4>
											{achievement.issuer && (
												<p className="text-blue-600 font-medium text-sm mt-1">
													{achievement.issuer}
												</p>
											)}
										</div>
									</div>

									{formattedDate && (
										<p className="text-slate-600 text-sm mb-3">
											{formattedDate}
										</p>
									)}

									{achievement.description && (
										<div
											dangerouslySetInnerHTML={{
												__html: achievement.description,
											}}
											className="text-slate-700 text-sm leading-relaxed"
										/>
									)}

									{achievement.url && (
										<a
											href={achievement.url}
											target="_blank"
											rel="noopener noreferrer"
											className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors text-sm mt-3"
										>
											<span className="mr-1">🔗</span>
											View Details
										</a>
									)}
								</div>
							);
						})}
					</div>
				</WebsiteSection>
			)}

			{/* Core Expertise */}
			{Object.keys(executiveSkills).length > 0 && (
				<WebsiteSection
					id="expertise"
					title="Core Expertise"
					className="max-w-6xl mx-auto px-6"
				>
					<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
						{Object.entries(executiveSkills).map(
							([category, categorySkills]) => (
								<div
									key={category}
									className="bg-white rounded-xl p-6 shadow-lg border border-slate-200 hover:shadow-xl transition-shadow"
								>
									<h3 className="text-xl font-bold text-slate-900 mb-4 pb-2 border-b border-slate-200">
										{category}
									</h3>
									<div className="space-y-3">
										{categorySkills.map((skill) => (
											<div
												key={skill.id}
												className="flex items-center justify-between"
											>
												<span className="text-slate-700 font-medium">
													{skill.name}
												</span>
												<div className="flex items-center gap-2">
													<div className="w-20 h-2 bg-slate-200 rounded-full overflow-hidden">
														<div
															className="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all"
															style={{ width: `${skill.proficiency || 0}%` }}
														/>
													</div>
													<span className="text-xs text-slate-500 w-8 text-right">
														{skill.proficiency || 0}%
													</span>
												</div>
											</div>
										))}
									</div>
								</div>
							),
						)}
					</div>
				</WebsiteSection>
			)}

			{/* Education */}
			{educations.length > 0 && (
				<WebsiteSection
					id="education"
					title="Education"
					className="max-w-6xl mx-auto px-6 bg-slate-50/50 py-16 rounded-3xl"
				>
					<WebsiteEducation educations={educations} />
				</WebsiteSection>
			)}

			{/* Contact */}
			<section id="contact" className="py-16 px-6 bg-slate-900 text-white">
				<div className="max-w-4xl mx-auto text-center">
					<h2 className="text-3xl font-bold mb-8">Get In Touch</h2>
					<div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
						<WebsiteContact
							email={resume.email}
							website={resume.website}
							location={location}
							className="text-white"
						/>
					</div>
				</div>
			</section>

			{/* Footer */}
			<footer className="bg-slate-800 text-white py-8 text-center">
				<div className="max-w-6xl mx-auto px-6">
					<p className="text-slate-300 mb-2">
						© {new Date().getFullYear()} {fullName}
					</p>
					<p className="text-slate-400 text-sm">
						Powered by{" "}
						<a
							href="https://quickcv.com"
							className="text-blue-400 hover:text-blue-300 transition-colors"
							target="_blank"
							rel="noopener noreferrer"
						>
							QuickCV
						</a>
					</p>
				</div>
			</footer>
		</div>
	);
};
