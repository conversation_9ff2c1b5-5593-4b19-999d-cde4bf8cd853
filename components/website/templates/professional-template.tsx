import {
	formatLocation,
	getFullName,
} from "@/components/resume/templates/base-components";
import Image from "next/image";
import React from "react";
import {
	WebsiteFooter,
	WebsiteProjects,
	WebsiteSection,
	WebsiteTemplateProps,
} from "./base-components";

export const ProfessionalTemplate: React.FC<WebsiteTemplateProps> = ({
	resume,
	website,
	className = "",
}) => {
	const fullName = getFullName(resume.firstName || "", resume.lastName || "");
	const location = formatLocation(resume.city || "", resume.country || "");
	const displayTitle = resume.jobTitle;
	const displayBio = resume.bio;

	return (
		<div className={`professional-template min-h-screen bg-white ${className}`}>
			{/* Header Section */}
			<header className="bg-gray-900 text-white py-12">
				<div className="max-w-4xl mx-auto px-6">
					<div className="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8">
						{/* Photo */}
						{resume.showPhoto && resume.photo && (
							<div className="flex-shrink-0">
								<Image
									alt={fullName}
									className="w-32 h-32 rounded-lg object-cover border-4 border-white shadow-lg"
									height={128}
									src={resume.photo}
									width={128}
								/>
							</div>
						)}

						{/* Info */}
						<div className="text-center md:text-left">
							<h1 className="text-4xl md:text-5xl font-bold mb-2">
								{fullName}
							</h1>
							<p className="text-xl md:text-2xl text-gray-300 mb-4">
								{displayTitle}
							</p>

							{/* Contact Info */}
							<div className="flex flex-col md:flex-row md:space-x-6 space-y-2 md:space-y-0 text-gray-300">
								{resume.email && (
									<div className="flex items-center justify-center md:justify-start">
										<span className="mr-2">✉️</span>
										<a
											href={`mailto:${resume.email}`}
											className="hover:text-white transition-colors"
										>
											{resume.email}
										</a>
									</div>
								)}
								{resume.website && (
									<div className="flex items-center justify-center md:justify-start">
										<span className="mr-2">🌐</span>
										<a
											href={resume.website}
											target="_blank"
											rel="noopener noreferrer"
											className="hover:text-white transition-colors"
										>
											{resume.website}
										</a>
									</div>
								)}
								{location && (
									<div className="flex items-center justify-center md:justify-start">
										<span className="mr-2">📍</span>
										<span>{location}</span>
									</div>
								)}
							</div>
						</div>
					</div>
				</div>
			</header>

			{/* Main Content */}
			<main className="max-w-4xl mx-auto px-6 py-12">
				{/* Professional Summary */}
				{displayBio && (
					<WebsiteSection title="Professional Summary" className="mb-12">
						<div className="bg-gray-50 p-6 rounded-lg border-l-4 border-gray-900">
							<div
								dangerouslySetInnerHTML={{ __html: displayBio }}
								className="text-gray-700 leading-relaxed text-lg"
							/>
						</div>
					</WebsiteSection>
				)}

				{/* Two Column Layout */}
				<div className="grid lg:grid-cols-3 gap-12">
					{/* Left Column - Main Content */}
					<div className="lg:col-span-2 space-y-12">
						{/* Experience */}
						{resume.experiences && resume.experiences.length > 0 && (
							<WebsiteSection title="Professional Experience">
								<div className="space-y-8">
									{resume.experiences.map((experience) => (
										<div
											key={experience.id}
											className="border-l-4 border-gray-300 pl-6"
										>
											<div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-2">
												<div>
													<h3 className="text-xl font-bold text-gray-900 mb-1">
														{experience.title}
													</h3>
													<p className="text-lg text-gray-700 font-semibold">
														{experience.company}
													</p>
												</div>
												<div className="text-gray-600 mt-1 sm:mt-0 sm:text-right">
													<p className="font-medium">
														{experience.startDate && experience.endDate
															? `${new Date(experience.startDate).getFullYear()} - ${experience.isCurrent
																? "Present"
																: new Date(
																	experience.endDate,
																).getFullYear()
															}`
															: ""}
													</p>
													{(experience.city || experience.country) && (
														<p className="text-sm">
															{formatLocation(
																experience.city || "",
																experience.country || "",
															)}
														</p>
													)}
												</div>
											</div>
											{experience.description && (
												<div
													dangerouslySetInnerHTML={{
														__html: experience.description,
													}}
													className="text-gray-700 leading-relaxed mt-3"
												/>
											)}
										</div>
									))}
								</div>
							</WebsiteSection>
						)}

						{/* Projects */}
						{resume.projects && resume.projects.length > 0 && (
							<WebsiteSection title="Key Projects">
								<WebsiteProjects projects={resume.projects} />
							</WebsiteSection>
						)}
					</div>

					{/* Right Column - Sidebar */}
					<div className="space-y-8">
						{/* Skills */}
						{resume.skills && resume.skills.length > 0 && (
							<div>
								<h3 className="text-xl font-bold text-gray-900 mb-4 border-b-2 border-gray-200 pb-2">
									Core Skills
								</h3>
								<div className="space-y-4">
									{Object.entries(
										resume.skills.reduce(
											(acc, skill) => {
												const category = skill.category || "Other";
												if (!acc[category]) acc[category] = [];
												acc[category].push(skill);
												return acc;
											},
											{} as Record<string, typeof resume.skills>,
										),
									).map(([category, categorySkills]) => (
										<div key={category}>
											<h4 className="font-semibold text-gray-800 mb-2">
												{category}
											</h4>
											<div className="flex flex-wrap gap-1">
												{categorySkills.map((skill) => (
													<span
														key={skill.id}
														className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm"
													>
														{skill.name}
													</span>
												))}
											</div>
										</div>
									))}
								</div>
							</div>
						)}

						{/* Education */}
						{resume.educations && resume.educations.length > 0 && (
							<div>
								<h3 className="text-xl font-bold text-gray-900 mb-4 border-b-2 border-gray-200 pb-2">
									Education
								</h3>
								<div className="space-y-4">
									{resume.educations.map((education) => (
										<div key={education.id}>
											<h4 className="font-semibold text-gray-900">
												{education.institution}
											</h4>
											<p className="text-gray-700 text-sm">
												{education.fieldOfStudy
													? `${education.degree} in ${education.fieldOfStudy}`
													: education.degree}
											</p>
											<p className="text-gray-600 text-sm">
												{education.startDate && education.endDate
													? `${new Date(education.startDate).getFullYear()} - ${education.isCurrent
														? "Present"
														: new Date(education.endDate).getFullYear()}`
													: ""}
											</p>
										</div>
									))}
								</div>
							</div>
						)}

						{/* Languages */}
						{resume.languages && resume.languages.length > 0 && (
							<div>
								<h3 className="text-xl font-bold text-gray-900 mb-4 border-b-2 border-gray-200 pb-2">
									Languages
								</h3>
								<div className="space-y-2">
									{resume.languages.map((language) => (
										<div
											key={language.id}
											className="flex justify-between items-center"
										>
											<span className="text-gray-700">{language.name}</span>
											<span className="text-gray-600 text-sm">
												{language.proficiency >= 90
													? "Native"
													: language.proficiency >= 70
														? "Fluent"
														: language.proficiency >= 50
															? "Intermediate"
															: "Basic"}
											</span>
										</div>
									))}
								</div>
							</div>
						)}

						{/* Certifications */}
						{resume.certifications && resume.certifications.length > 0 && (
							<div>
								<h3 className="text-xl font-bold text-gray-900 mb-4 border-b-2 border-gray-200 pb-2">
									Certifications
								</h3>
								<div className="space-y-3">
									{resume.certifications.map((cert) => (
										<div key={cert.id}>
											<h4 className="font-semibold text-gray-900 text-sm">
												{cert.title}
											</h4>
											<p className="text-gray-700 text-sm">{cert.issuer}</p>
											{cert.dateReceived && (
												<p className="text-gray-600 text-xs">
													{new Date(cert.dateReceived).getFullYear()}
												</p>
											)}
										</div>
									))}
								</div>
							</div>
						)}
					</div>
				</div>
			</main>

			{/* Footer */}
			<WebsiteFooter fullName={fullName} className="mt-12" />
		</div>
	);
};
