import Image from "next/image";
import React from "react";
import { WebsiteTemplate } from "@/db/schema";
import { WebsiteTemplateProps } from "./base-components";
import { ExecutiveTemplate } from "./executive-template";
import { PortfolioTemplate } from "./portfolio-template";
import { ProfessionalTemplate } from "./professional-template";

// Website template registry mapping slugs to components
export const websiteTemplateRegistry: Record<
	string,
	React.FC<WebsiteTemplateProps>
> = {
	elegant: PortfolioTemplate,
	rocket: ProfessionalTemplate,
	modern: ExecutiveTemplate,
};

// Get website template component by slug
export const getWebsiteTemplate = (
	slug: string,
): React.FC<WebsiteTemplateProps> | null => {
	return websiteTemplateRegistry[slug] || null;
};

// Get all available website templates
export const getAvailableWebsiteTemplates = () => {
	return Object.keys(websiteTemplateRegistry);
};

// Website template selector component
export interface WebsiteTemplateSelectorProps {
	selectedTemplate?: string;
	onTemplateSelect: (slug: string) => void;
	className?: string;
	websiteTemplates: WebsiteTemplate[];
}

export const WebsiteTemplateSelector: React.FC<
	WebsiteTemplateSelectorProps
> = ({
	selectedTemplate,
	onTemplateSelect,
	className = "",
	websiteTemplates,
}) => {
	console.log(websiteTemplates);
	return (
		<div className={`website-template-selector ${className}`}>
			<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
				{websiteTemplates?.map((template) => (
					<div
						key={template.slug}
						className={`template-option border-2 rounded-lg cursor-pointer transition-all hover:shadow-lg relative overflow-hidden ${
							selectedTemplate === template.slug
								? "border-blue-500 ring-2 ring-blue-500 ring-opacity-50"
								: "border-gray-200 hover:border-gray-300"
						}`}
						onClick={() => onTemplateSelect(template.slug)}
					>
						{/* Template Thumbnail */}
						<div className="">
							{/* Mock template preview based on slug */}
							<Image
								src={template.preview}
								width={1280}
								height={695}
								alt={template.slug}
							/>
						</div>

						{/* Selection Indicator */}
						{selectedTemplate === template.slug && (
							<div className="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
								<svg
									className="w-4 h-4 text-white"
									fill="currentColor"
									viewBox="0 0 20 20"
								>
									<path
										fillRule="evenodd"
										d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
										clipRule="evenodd"
									/>
								</svg>
							</div>
						)}
					</div>
				))}
			</div>
		</div>
	);
};

// Website template preview component
export interface WebsiteTemplatePreviewProps {
	templateSlug: string;
	resume: any;
	website: any;
	className?: string;
}

export const WebsiteTemplatePreview: React.FC<WebsiteTemplatePreviewProps> = ({
	templateSlug,
	resume,
	website,
	className = "",
}) => {
	const TemplateComponent = getWebsiteTemplate(templateSlug);

	if (!TemplateComponent) {
		return (
			<div
				className={`template-preview-error p-8 text-center text-gray-500 ${className}`}
			>
				Template not found: {templateSlug}
			</div>
		);
	}

	return (
		<div className={`website-template-preview ${className}`}>
			<TemplateComponent resume={resume} website={website} />
		</div>
	);
};
