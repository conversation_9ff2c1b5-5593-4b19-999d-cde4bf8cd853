"use client";

import { DatePicker } from "@heroui/react";
import { DateValue, parseDate } from "@internationalized/date";
import React from "react";

export default function DatePickerComponent({
	className,
	defaultValue,
	label,
	name,
	onChange,
}: {
	className?: string;
	defaultValue?: string;
	label?: string;
	name?: string;
	onChange?: (value: string) => void;
}) {
	const [value, setValue] = React.useState<DateValue | null | undefined>(() => {
		if (!defaultValue) return undefined;

		try {
			// Handle ISO 8601 date strings by extracting just the date part
			let dateString = defaultValue;
			if (defaultValue.includes("T")) {
				dateString = defaultValue.split("T")[0];
			}

			return parseDate(dateString) as unknown as DateValue;
		} catch (error) {
			console.warn("Failed to parse date:", defaultValue, error);
			return undefined;
		}
	});

	return (
		<DatePicker
			className={className}
			label={label}
			name={name}
			value={value as any}
			onChange={(newValue) => {
				setValue(newValue as any);
				if (onChange && newValue) {
					onChange(newValue.toString());
				}
			}}
		/>
	);
}
