"use client";

import { useLocale } from "next-intl";

/**
 * Demonstration component showing tailwindcss-rtl utilities in action
 *
 * Key tailwindcss-rtl classes:
 * - text-start: text-align: left in LTR, text-align: right in RTL
 * - text-end: text-align: right in LTR, text-align: left in RTL
 * - ms-*: margin-left in LTR, margin-right in RTL
 * - me-*: margin-right in LTR, margin-left in RTL
 * - ps-*: padding-left in LTR, padding-right in RTL
 * - pe-*: padding-right in LTR, padding-left in RTL
 * - start-*: left in LTR, right in RTL
 * - end-*: right in LTR, left in RTL
 * - border-s-*: border-left in LTR, border-right in RTL
 * - border-e-*: border-right in LTR, border-left in RTL
 */
export const RTLDemo: React.FC = () => {
	const locale = useLocale();
	const isRTL = locale === "ar";

	return (
		<div className="p-6 max-w-2xl mx-auto" dir={isRTL ? "rtl" : "ltr"}>
			<h2 className="text-2xl font-bold mb-6 text-start">
				RTL Demo - tailwindcss-rtl in Action
			</h2>

			{/* Text alignment */}
			<div className="mb-6 p-4 bg-gray-100 rounded">
				<h3 className="text-lg font-semibold mb-2 text-start">
					Text Alignment
				</h3>
				<p className="text-start mb-2">
					This text uses &apos;text-start&apos; - starts from the reading
					direction
				</p>
				<p className="text-end">
					This text uses &apos;text-end&apos; - aligns to the end of reading
					direction
				</p>
			</div>

			{/* Margins and spacing */}
			<div className="mb-6 p-4 bg-blue-100 rounded">
				<h3 className="text-lg font-semibold mb-2 text-start">
					Margins & Spacing
				</h3>
				<div className="flex items-center mb-2">
					<span className="bg-red-500 text-white px-2 py-1 rounded">
						Item 1
					</span>
					<span className="bg-green-500 text-white px-2 py-1 rounded ms-4">
						Item 2 (ms-4)
					</span>
					<span className="bg-blue-500 text-white px-2 py-1 rounded me-4">
						Item 3 (me-4)
					</span>
				</div>
			</div>

			{/* Padding */}
			<div className="mb-6 p-4 bg-yellow-100 rounded">
				<h3 className="text-lg font-semibold mb-2 text-start">Padding</h3>
				<div className="bg-purple-500 text-white ps-8 pe-2 py-2 rounded">
					ps-8 pe-2: Start padding 8, End padding 2
				</div>
			</div>

			{/* Borders */}
			<div className="mb-6 p-4 bg-green-100 rounded">
				<h3 className="text-lg font-semibold mb-2 text-start">Borders</h3>
				<div className="border-s-4 border-s-red-500 ps-4 mb-2">
					border-s-4: Start border (left in LTR, right in RTL)
				</div>
				<div className="border-e-4 border-e-blue-500 pe-4">
					border-e-4: End border (right in LTR, left in RTL)
				</div>
			</div>

			{/* Position */}
			<div className="mb-6 p-4 bg-pink-100 rounded relative h-20">
				<h3 className="text-lg font-semibold mb-2 text-start">Positioning</h3>
				<div className="absolute start-4 top-12 bg-orange-500 text-white px-2 py-1 rounded text-sm">
					start-4: Positioned from start
				</div>
				<div className="absolute end-4 top-12 bg-teal-500 text-white px-2 py-1 rounded text-sm">
					end-4: Positioned from end
				</div>
			</div>

			<div className="text-center text-sm text-gray-600">
				Current locale: {locale} ({isRTL ? "RTL" : "LTR"})
			</div>
		</div>
	);
};
