"use client";

import React, { useCallback, useEffect, useState } from "react";
import { Autosave } from "react-autosave";
import { updateNestedItem } from "@/actions/resumes";
import { useFormStore } from "@/lib/form-store";
import { AutoSaveIndicator } from "./AutoSaveIndicator";

interface AutosaveNestedFormProps {
	children: React.ReactNode;
	item: any;
	baseRoute: (id: number) => string;
	resumeId: number;
	className?: string;
}

export const AutosaveNestedForm: React.FC<AutosaveNestedFormProps> = ({
	children,
	item,
	baseRoute,
	resumeId,
	className = "",
}) => {
	const { formData, setFormData } = useFormStore();
	const [isSaving, setIsSaving] = useState(false);
	const [lastSaved, setLastSaved] = useState<Date | undefined>();

	// Update form data when item prop changes
	useEffect(() => {
		setFormData(item);
	}, [item, setFormData]);

	const handleAutoSave = useCallback(
		async (data: any) => {
			try {
				if (!data.id) {
					return false; // Can't save without an ID
				}

				setIsSaving(true);
				const result = await updateNestedItem(
					baseRoute(resumeId),
					data.id,
					resumeId,
					data,
				);

				if (result.success) {
					setLastSaved(new Date());
				}

				return result.success;
			} catch (error) {
				console.error("Nested form auto-save failed:", error);
				return false;
			} finally {
				setIsSaving(false);
			}
		},
		[baseRoute, resumeId],
	);

	return (
		<div className={className}>
			<Autosave data={formData} interval={2000} onSave={handleAutoSave} />
			<div className="flex justify-between items-start mb-4">
				<div className="flex-1">{children}</div>
				<AutoSaveIndicator
					isSaving={isSaving}
					lastSaved={lastSaved}
					className="ml-4 flex-shrink-0"
				/>
			</div>
		</div>
	);
};
