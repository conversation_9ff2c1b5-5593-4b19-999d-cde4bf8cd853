"use client";
import { But<PERSON>, cn } from "@heroui/react";
import { Icon } from "@iconify/react";
import Color from "@tiptap/extension-color";
import ListItem from "@tiptap/extension-list-item";
import Placeholder from "@tiptap/extension-placeholder";
import TextStyle from "@tiptap/extension-text-style";
import { Editor, EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { useEffect, useState } from "react";

interface RichEditorProps {
	value: string;
	placeholder?: string;
	className?: string;
	minHeight?: string;
	readOnly?: boolean;
	id?: string;
	name?: string;
	onChange?: (value: string) => void;
}

export function RichEditor({
	value,
	name,
	placeholder = "Write something...",
	className,
	minHeight = "200px",
	readOnly = false,
	id,
	onChange,
}: RichEditorProps) {
	const [content, setContent] = useState(value);

	const editor = useEditor({
		immediatelyRender: false,
		extensions: [
			Color.configure({ types: [TextStyle.name, ListItem.name] }),
			TextStyle,
			StarterKit.configure({
				heading: {
					levels: [1, 2, 3],
				},
				bulletList: {
					keepMarks: true,
					keepAttributes: false, // TODO : Making this as `false` becase marks are not preserved when I try to preserve attrs, awaiting a bit of help
				},
				orderedList: {
					keepMarks: true,
					keepAttributes: false, // TODO : Making this as `false` becase marks are not preserved when I try to preserve attrs, awaiting a bit of help
				},
			}),

			Placeholder.configure({
				placeholder,
			}),
		],
		editorProps: {
			attributes: {
				class:
					"prose prose-sm dark:prose-invert max-w-none h-full min-h-[200px] p-2",
			},
		},
		content: value,
		editable: !readOnly,
		onUpdate: ({ editor }) => {
			const html = editor.getHTML();
			setContent(html);
			if (onChange) {
				onChange(html);
			}
		},
	});

	// Update editor content when value prop changes
	useEffect(() => {
		if (editor && value !== editor.getHTML()) {
			editor.commands.setContent(value || "");
		}
	}, [value, editor]);

	if (!editor) {
		return null;
	}

	return (
		<div
			className={cn(
				"rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800",
				className,
			)}
		>
			<div className="p-4 focus:outline-none" style={{ minHeight }}>
				<MenuBar editor={editor} />
				<EditorContent
					className="tiptop text-gray-900 dark:text-gray-100"
					editor={editor}
					id={id}
				/>

				{/* Hidden input to include content in form submission */}
				<input name={name} type="hidden" value={content || ""} />
			</div>
		</div>
	);
}

const MenuBar = ({ editor }: { editor: Editor }) => {
	return (
		<div className="flex flex-wrap items-center gap-1 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 p-1 rounded-t-md">
			<Button
				className={
					editor.isActive("bold")
						? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
						: ""
				}
				size="sm"
				title="Bold"
				type="button"
				variant="ghost"
				onPress={() => editor.chain().focus().toggleBold().run()}
			>
				<Icon height={16} icon="mdi:format-bold" width={16} />
			</Button>
			<Button
				className={
					editor.isActive("paragraph")
						? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
						: ""
				}
				size="sm"
				title="Paragraph"
				type="button"
				variant="ghost"
				onPress={() => editor.chain().focus().setParagraph().run()}
			>
				<Icon height={16} icon="mdi:format-paragraph" width={16} />
			</Button>
			<Button
				className={
					editor.isActive("heading", { level: 1 })
						? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
						: ""
				}
				size="sm"
				title="Heading 1"
				type="button"
				variant="ghost"
				onPress={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
			>
				<Icon height={16} icon="mdi:format-header-1" width={16} />
			</Button>
			<Button
				className={
					editor.isActive("heading", { level: 2 })
						? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
						: ""
				}
				size="sm"
				title="Heading 2"
				type="button"
				variant="ghost"
				onPress={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
			>
				<Icon height={16} icon="mdi:format-header-2" width={16} />
			</Button>
			<Button
				className={
					editor.isActive("heading", { level: 3 })
						? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
						: ""
				}
				size="sm"
				title="Heading 3"
				type="button"
				variant="ghost"
				onPress={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
			>
				<Icon height={16} icon="mdi:format-header-3" width={16} />
			</Button>
			<Button
				className={
					editor.isActive("heading", { level: 4 })
						? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
						: ""
				}
				size="sm"
				title="Heading 4"
				type="button"
				variant="ghost"
				onPress={() => editor.chain().focus().toggleHeading({ level: 4 }).run()}
			>
				<Icon height={16} icon="mdi:format-header-4" width={16} />
			</Button>
			<Button
				className={
					editor.isActive("heading", { level: 5 })
						? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
						: ""
				}
				size="sm"
				title="Heading 5"
				type="button"
				variant="ghost"
				onPress={() => editor.chain().focus().toggleHeading({ level: 5 }).run()}
			>
				<Icon height={16} icon="mdi:format-header-5" width={16} />
			</Button>
			<Button
				className={
					editor.isActive("heading", { level: 6 })
						? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
						: ""
				}
				size="sm"
				title="Heading 6"
				type="button"
				variant="ghost"
				onPress={() => editor.chain().focus().toggleHeading({ level: 6 }).run()}
			>
				<Icon height={16} icon="mdi:format-header-6" width={16} />
			</Button>
			<Button
				className={
					editor.isActive("italic")
						? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
						: ""
				}
				size="sm"
				title="Italic"
				type="button"
				variant="ghost"
				onPress={() => editor.chain().focus().toggleItalic().run()}
			>
				<Icon height={16} icon="mdi:format-italic" width={16} />
			</Button>
			<div className="h-6 w-px bg-gray-300 dark:bg-gray-600" />
			<Button
				className={
					editor.isActive("bulletList")
						? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
						: ""
				}
				size="sm"
				title="Bullet List"
				type="button"
				variant="ghost"
				onPress={() => {
					editor.chain().focus().toggleBulletList().run();
				}}
			>
				<Icon height={16} icon="mdi:format-list-bulleted" width={16} />
			</Button>
			<Button
				className={
					editor.isActive("orderedList")
						? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
						: ""
				}
				size="sm"
				title="Ordered List"
				type="button"
				variant="ghost"
				onPress={() => editor.chain().focus().toggleOrderedList().run()}
			>
				<Icon height={16} icon="mdi:format-list-numbered" width={16} />
			</Button>

			<Button
				disabled={!editor.can().undo()}
				size="sm"
				title="Undo"
				type="button"
				variant="ghost"
				onPress={() => editor.chain().focus().undo().run()}
			>
				<Icon height={16} icon="mdi:undo" width={16} />
			</Button>
			<Button
				disabled={!editor.can().redo()}
				size="sm"
				title="Redo"
				type="button"
				variant="ghost"
				onPress={() => editor.chain().focus().redo().run()}
			>
				<Icon height={16} icon="mdi:redo" width={16} />
			</Button>
		</div>
	);
};
