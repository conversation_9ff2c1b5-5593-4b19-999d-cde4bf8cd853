// submit-button.tsx
"use client";

import { Button, Progress } from "@heroui/react";
import { useFormStatus } from "react-dom";

export default function SubmitButton({
	children,
	className,
	size,
	progress,
	progressLabel,
}: {
	children?: React.ReactNode;
	className?: string;
	size?: "lg" | "md" | "sm";
	progress?: number; // 0-100 for determinate progress
	progressLabel?: string;
}) {
	const { pending } = useFormStatus();
	const showProgress = pending && typeof progress === "number";

	return (
		<div className="flex flex-col gap-2">
			<Button
				className={className}
				color="primary"
				disabled={pending}
				fullWidth={true}
				isLoading={pending}
				size={size}
				type="submit"
			>
				{children}
			</Button>
			{showProgress && (
				<Progress
					aria-label={progressLabel || "Form submission progress"}
					color="primary"
					size="sm"
					value={progress}
					showValueLabel={true}
					className="w-full"
					label={progressLabel}
				/>
			)}
		</div>
	);
}
