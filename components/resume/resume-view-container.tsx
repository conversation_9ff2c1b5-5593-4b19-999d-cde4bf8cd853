"use client";

import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { FullResume } from "@/db/schema";
import ResumeCard from "./resume-card";
import ResumePageHeader from "./resume-page-header";

interface ResumeViewContainerProps {
	resumes: FullResume[];
}

export default function ResumeViewContainer({
	resumes,
}: ResumeViewContainerProps) {
	const [updatedResumes, setUpdatedResumes] = useState<FullResume[]>(resumes);
	const t = useTranslations("resumes");

	// Update local state when props change
	useEffect(() => {
		setUpdatedResumes(resumes);
	}, [resumes]);

	return (
		<>
			<ResumePageHeader resumeCount={updatedResumes.length} />

			<div className="flex flex-wrap gap-6 justify-start">
				{updatedResumes.map((resume, index) => (
					<div
						key={resume.id}
						className="animate-in fade-in-0 slide-in-from-bottom-4 max-w-[260px]"
						style={{
							animationDelay: `${index * 100}ms`,
							animationFillMode: "both",
						}}
					>
						<ResumeCard resume={resume} />
					</div>
				))}
			</div>

			{/* No results message */}
			{updatedResumes.length === 0 && (
				<div className="text-center py-12">
					<div className="w-16 h-16 bg-content2 rounded-full flex items-center justify-center mx-auto mb-4">
						<Icon
							className="w-8 h-8 text-foreground/40"
							icon="lucide:search-x"
						/>
					</div>
					<h3 className="text-lg font-semibold text-foreground mb-2">
						{t("no_results_title")}
					</h3>
					<p className="text-foreground/60">{t("no_results_description")}</p>
				</div>
			)}
		</>
	);
}
