"use client";

import { useRouter } from "@bprogress/next/app";
import { useAuth } from "@clerk/nextjs";
import {
	Button,
	Form,
	Input,
	Modal,
	ModalBody,
	ModalContent,
	ModalHeader,
	useDisclosure,
} from "@heroui/react";
import { useLocale, useTranslations } from "next-intl";
import React from "react";
import { createResume } from "@/actions/resumes";
import { routes } from "@/config/path-constants";
import { createEmptyResume } from "@/lib/utils";
import SubmitButton from "../submit-button";

export default function CreateResumeButton({
	className,
	size,
	text,
}: {
	className?: string;
	size?: "lg" | "md" | "sm";
	text?: string;
}) {
	const { isOpen, onOpen, onOpenChange } = useDisclosure();
	const { isSignedIn } = useAuth();
	const t = useTranslations("resumes");
	const router = useRouter();
	const locale = useLocale();

	const handleButtonClick = () => {
		// if (!isSignedIn) {
		// 	router.push(`/${locale}/sign-in`);
		// 	return;
		// }
		onOpen();
	};

	return (
		<>
			<Button
				className={className}
				color="primary"
				radius="full"
				size={size}
				variant="shadow"
				onPress={handleButtonClick}
			>
				{text || t("get_started")}
			</Button>
			<Modal isOpen={isOpen} onOpenChange={onOpenChange}>
				<ModalContent>
					{(onClose) => (
						<>
							<ModalHeader className="flex flex-col gap-1 justify-center items-center">
								{t("create_resume")}
							</ModalHeader>
							<ModalBody>
								<Form
									action={async (formData) => {
										const title = formData.get("title");
										if (!title) return;
										const resume = await createResume(
											createEmptyResume(title as string),
										);
										router.push(routes.resumeEditPath(resume.id, locale));
										onClose();
									}}
									className="flex flex-col gap-4 p-4"
								>
									<Input
										label={t("title")}
										name="title"
										placeholder={t("enter_resume_title")}
										type="text"
										variant="bordered"
									/>
									<SubmitButton className="w-full self-center" size="sm">
										{t("create")}
									</SubmitButton>
								</Form>
							</ModalBody>
						</>
					)}
				</ModalContent>
			</Modal>
		</>
	);
}
