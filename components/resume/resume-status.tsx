"use client";

import { Chip } from "@heroui/react";
import { useTranslations } from "next-intl";
import { FullResume } from "@/db/schema";

interface ResumeStatusProps {
	resume: FullResume;
}

export default function ResumeStatus({ resume }: ResumeStatusProps) {
	const t = useTranslations("resumes");

	const lastModified = resume.updatedAt || resume.createdAt;
	if (!lastModified) return null;

	const daysSinceUpdate = Math.floor(
		(Date.now() - new Date(lastModified).getTime()) / (1000 * 60 * 60 * 24),
	);

	const getStatusText = () => {
		if (daysSinceUpdate === 0) return t("today");
		if (daysSinceUpdate === 1) return t("yesterday");
		return t("days_ago", { count: daysSinceUpdate });
	};

	const getStatusColor = () => {
		if (daysSinceUpdate <= 1) return "success";
		if (daysSinceUpdate <= 7) return "warning";
		return "default";
	};

	return (
		<Chip
			className="text-xs shadow-sm"
			color={getStatusColor()}
			size="sm"
			variant="flat"
		>
			{getStatusText()}
		</Chip>
	);
}
