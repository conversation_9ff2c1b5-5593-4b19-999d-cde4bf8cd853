"use client";

import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>eader,
	useDisclosure,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import React from "react";
import { FullResume, Template } from "@/db/schema";
import { ResumeCustomizationForm } from "./ResumeCustomizationForm";

interface ResumeCustomizationDrawerProps {
	data: FullResume;
	templates: Template[];
}

export const ResumeCustomizationDrawer: React.FC<
	ResumeCustomizationDrawerProps
> = ({ data, templates }) => {
	const { isOpen, onOpen, onOpenChange } = useDisclosure();
	const t = useTranslations();
	return (
		<>
			{/* Trigger Button */}
			<Button
				className="fixed top-20 start-8 z-40 shadow-lg"
				color="secondary"
				startContent={<Icon icon="lucide:palette" />}
				variant="shadow"
				onPress={onOpen}
			>
				{t("preview.customize")}
			</Button>

			{/* Drawer Modal */}
			<Modal
				classNames={{
					base: "justify-end sm:m-0 p-0 h-dvh max-h-full w-[400px] sm:w-[450px]",
					wrapper: "items-start justify-end !w-[400px] sm:!w-[450px]",
					body: "p-0",
					closeButton: "z-50 top-4 right-4",
				}}
				isOpen={isOpen}
				motionProps={{
					variants: {
						enter: {
							x: 0,
							transition: {
								duration: 0.3,
								ease: "easeOut",
							},
						},
						exit: {
							x: 400,
							transition: {
								duration: 0.2,
								ease: "easeOut",
							},
						},
					},
				}}
				radius="none"
				scrollBehavior="inside"
				onOpenChange={onOpenChange}
			>
				<ModalContent>
					<ModalHeader className="flex flex-col gap-1 border-b border-gray-200 dark:border-gray-700 p-4">
						<div className="flex items-center gap-3">
							<div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary">
								<Icon className="text-white" icon="lucide:palette" />
							</div>
							<div>
								<h2 className="text-lg font-bold text-foreground">
									{t("settings.customize_resume")}
								</h2>
								<p className="text-sm text-gray-600 dark:text-gray-400">
									{t("settings.customize_description")}
								</p>
							</div>
						</div>
					</ModalHeader>
					<ModalBody className="p-4">
						<ResumeCustomizationForm data={data} templates={templates} />
					</ModalBody>
				</ModalContent>
			</Modal>
		</>
	);
};
