"use client";

import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import CreateResumeButton from "./createResumeButton";

export default function ResumeEmptyState() {
	const t = useTranslations("resumes");
	return (
		<div className="flex flex-col items-center justify-center py-20 px-4">
			<div className="relative mb-8">
				<div className="w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center">
					<Icon className="w-12 h-12 text-primary/60" icon="lucide:file-text" />
				</div>
				<div className="absolute -top-2 -right-2 w-8 h-8 bg-warning/20 rounded-full flex items-center justify-center">
					<Icon className="w-4 h-4 text-warning" icon="lucide:plus" />
				</div>
			</div>
			<h3 className="text-2xl font-bold text-foreground mb-2">
				{t("no_resumes_title")}
			</h3>
			<p className="text-foreground/60 text-center max-w-md mb-8">
				{t("no_resumes_description")}
			</p>
			<CreateResumeButton className="px-8" size="lg" />
		</div>
	);
}
