"use client";

import { Accordion, AccordionI<PERSON>, Button, Form, Input } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import { useMemo } from "react";
import { addNestedItem, updateResume } from "@/actions/resumes";
import { useSchemas } from "@/config/schemas-i18n";
import { prepareNestedForms, RenderForm } from "@/lib/form";
import { upperCaseFirstLetter } from "@/lib/utils";
import { FullResume } from "@/db/schema";
import AccordionFormList from "../AccordionFormList";
import PersonalFormFields from "./personalFormFields";

export const ResumeEditForm = ({ data: initialData }: { data: FullResume }) => {
	const schemas = useSchemas();
	const t = useTranslations();

	const nested_forms = useMemo(
		() => prepareNestedForms(initialData, schemas),
		[initialData, schemas],
	);

	return (
		<div className="flex flex-col gap-4 w-full h-full">
			<Form action={updateResume} encType="multipart/form-data">
				<Input
					defaultValue={initialData?.id.toString()}
					name="id"
					type="hidden"
				/>

				<PersonalFormFields data={initialData} />
				<div className="w-full mt-4">
					{nested_forms.map((form, formIndex) => {
						const itemKey = `${form.name}-${formIndex}`;
						return (
							<div key={itemKey} className="mb-4">
								<Accordion isCompact variant="splitted">
									<AccordionItem
										key={itemKey}
										aria-label={form.schema?.collection || form.name}
										startContent={addItemButton({
											onAddNew: () =>
												addNestedItem(
													form.route(initialData.id),
													initialData.id,
												),
										})}
										subtitle={form.schema?.description || ""}
										textValue={form.schema?.entity || form.name}
										title={upperCaseFirstLetter(
											t(`forms.${form.schema?.entity || form.name}`),
										)}
									>
										<AccordionFormList
											baseRoute={form.baseRoute}
											className="p-4"
											items={
												form.items
													?.filter((item) => item.id != null)
													.map((item, index) => ({
														...item,
														id: item.id!,
														sort: index,
													})) || []
											}
											keyName={form.keyName}
											renderForm={(item, index) => (
												<RenderForm
													index={index}
													item={item}
													schema={form.schema}
												/>
											)}
											resumeId={initialData.id}
											titlePrefix={t(
												`forms.singular.${form.schema?.entity || form.name}`,
											)}
										/>
									</AccordionItem>
								</Accordion>
							</div>
						);
					})}
				</div>
			</Form>
		</div>
	);
};

const addItemButton = ({ onAddNew }: { onAddNew: () => void }) => {
	return (
		<Button
			isIconOnly
			color="primary"
			size="sm"
			startContent={<Icon icon="lucide:plus" />}
			variant="flat"
			onPress={onAddNew}
		/>
	);
};
