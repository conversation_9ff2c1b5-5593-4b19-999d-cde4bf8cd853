"use client";

import React, { useCallback, useState } from "react";
import { Autosave } from "react-autosave";
import { updateResumeForAutosave } from "@/actions/resumes";
import { ColorSchemeSelector } from "@/components/shared/ColorSchemeSelector";
import { TemplateSelector } from "@/components/shared/TemplateSelector";
import { ColorSchemeId } from "@/config/color-schemes";
import { Template } from "@/db/schema";
import { FullResume } from "@/db/schema";

interface ResumeCustomizationFormProps {
	data: FullResume;
	templates: Template[];
}

export const ResumeCustomizationForm: React.FC<
	ResumeCustomizationFormProps
> = ({ data, templates }) => {
	const [formData, setFormData] = useState({
		template_id: data.template_id,
		colorScheme: data.colorScheme || "blue",
	});

	const handleTemplateChange = useCallback((templateId: number) => {
		setFormData((prev) => ({ ...prev, template_id: templateId }));
	}, []);

	const handleColorSchemeChange = useCallback((colorScheme: ColorSchemeId) => {
		setFormData((prev) => ({ ...prev, colorScheme }));
	}, []);

	const handleAutoSave = useCallback(
		async (customizationData: typeof formData) => {
			try {
				const result = await updateResumeForAutosave({
					id: data.id,
					...customizationData,
				});
				return result;
			} catch (error) {
				console.error("Customization auto-save failed:", error);
				return false;
			}
		},
		[data.id],
	);

	return (
		<div className="space-y-6 py-4">
			<Autosave data={formData} interval={2000} onSave={handleAutoSave} />
			{/* Colors Section */}
			<ColorSchemeSelector
				selectedColorScheme={formData.colorScheme as ColorSchemeId}
				onColorSchemeChange={handleColorSchemeChange}
			/>
			{/* Templates Section */}
			<TemplateSelector
				selectedTemplateId={formData.template_id}
				showCategory={false}
				templates={templates}
				onTemplateSelect={handleTemplateChange}
			/>
		</div>
	);
};
