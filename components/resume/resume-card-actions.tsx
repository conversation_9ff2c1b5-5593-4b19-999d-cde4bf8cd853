"use client";

import { useRouter } from "@bprogress/next/app";
import {
	Button,
	Dropdown,
	DropdownItem,
	DropdownMenu,
	DropdownTrigger,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import React, { useState } from "react";
import { deleteResume, duplicateResume } from "@/actions/resumes";
import { routes } from "@/config/path-constants";

interface ResumeCardActionsProps {
	resumeId: number;
}

export default function ResumeCardActions({
	resumeId,
}: ResumeCardActionsProps) {
	const t = useTranslations("resumes.actions");
	const router = useRouter();
	const [isLoading, setIsLoading] = useState(false);

	const handleDuplicate = async () => {
		setIsLoading(true);
		try {
			const result = await duplicateResume(resumeId);
			if (result.success) {
				//TODO handle toast
			} else {
				console.error("Failed to duplicate resume:", result.error);
			}
		} catch (error) {
			console.error("Error duplicating resume:", error);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dropdown>
			<DropdownTrigger>
				<Button
					isIconOnly
					aria-label={t("more_options")}
					className="transition-all duration-200 hover:scale-105"
					size="sm"
					variant="light"
					color="default"
					isLoading={isLoading}
				>
					<Icon height="16" icon="lucide:more-horizontal" width="16" />
				</Button>
			</DropdownTrigger>
			<DropdownMenu aria-label="Resume actions">
				<DropdownItem
					key="edit"
					startContent={<Icon className="w-4 h-4" icon="lucide:edit-3" />}
					onPress={() => router.push(routes.resumeEditPath(resumeId))}
				>
					{t("edit")}
				</DropdownItem>

				<DropdownItem
					key="website"
					startContent={<Icon className="w-4 h-4" icon="lucide:globe" />}
					onPress={() => router.push(`/resumes/${resumeId}/website`)}
				>
					{t("create_website")}
				</DropdownItem>
				<DropdownItem
					key="duplicate"
					startContent={<Icon className="w-4 h-4" icon="lucide:copy" />}
					onPress={handleDuplicate}
					isDisabled={isLoading}
				>
					{t("duplicate")}
				</DropdownItem>

				<DropdownItem
					key="delete"
					className="text-danger"
					onPress={async () => {
						await deleteResume(resumeId);
					}}
					color="danger"
					startContent={<Icon className="w-4 h-4" icon="lucide:trash-2" />}
				>
					{t("delete")}
				</DropdownItem>
			</DropdownMenu>
		</Dropdown>
	);
}
