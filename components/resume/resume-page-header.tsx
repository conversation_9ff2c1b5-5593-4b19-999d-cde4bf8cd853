"use client";

import { useTranslations } from "next-intl";
import CreateResumeButton from "./createResumeButton";

interface PageHeaderProps {
	resumeCount: number;
}

export default function ResumePageHeader({ resumeCount }: PageHeaderProps) {
	const t = useTranslations("resumes");
	return (
		<div className="mb-8">
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
				<div>
					<h1 className="text-3xl font-bold text-foreground mb-2">
						{t("page_title")}
					</h1>
					<p className="text-foreground/60">
						{t("page_subtitle", { count: resumeCount })}
					</p>
				</div>
				<div className="flex items-center gap-3">
					<CreateResumeButton size="md" text="Add New Resume" />
				</div>
			</div>
		</div>
	);
}
