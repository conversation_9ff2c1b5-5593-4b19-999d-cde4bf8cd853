import Image from "next/image";
import React from "react";
import {
	CertificationItem,
	ContactInfo,
	EducationItem,
	ExperienceItem,
	formatLocation,
	getFullName,
	LanguageItem,
	ProfessionalSummary,
	ProjectItem,
	ResumeSection,
	SkillsSection,
	SocialProfile,
	TemplateProps,
	useSectionTranslations,
	useTemplateLocale,
} from "./base-components";

// Custom main section for pikachu template - orange theme
const PikachuMainSection: React.FC<{
	title: string;
	children: React.ReactNode;
}> = ({ title, children }) => (
	<ResumeSection
		accentColor="#f97316" // orange-500
		className="mb-6"
		title={title}
		variant="default"
	>
		<div className="[&_.section-title]:border-b [&_.section-title]:border-orange-400">
			{children}
		</div>
	</ResumeSection>
);

// Custom sidebar section for pikachu template - orange theme
const PikachuSidebarSection: React.FC<{
	title: string;
	children: React.ReactNode;
}> = ({ title, children }) => (
	<section className="mb-6 sidebar-section">
		<h3 className="text-lg font-bold mb-3 text-gray-900 border-b border-orange-400 pb-1 section-title">
			{title}
		</h3>
		{children}
	</section>
);

/**
 * Pikachu Resume Template - Two-Column Layout with Orange Header
 * - Photo on left, orange header with name/title/contact on right
 * - Left sidebar for secondary info (Profiles, Skills, Certifications, etc.)
 * - Right main content for primary sections (Summary, Experience, etc.)
 * - Orange/yellow color scheme matching reference design
 * - Clean typography and professional layout
 * - ATS-friendly structure with proper hierarchy
 */
export const PikachuTemplate: React.FC<TemplateProps> = ({
	resume,
	className = "",
}) => {
	const locale = useTemplateLocale();
	const sectionTitles = useSectionTranslations();
	const fullName = getFullName(resume.firstName, resume.lastName, locale);
	const location = formatLocation(resume.city, resume.country);

	return (
		<div
			className={`pikachu-template bg-white text-gray-900 font-sans w-full ${className}`}
		>
			{/* Page wrapper with A4 proportions */}
			<div className="min-h-[297mm] w-full min-w-[210mm] mx-auto bg-white">
				{/* Two-Column Layout - Full Height */}
				<div className="flex min-h-[297mm]">
					{/* Left Column - Photo and Sidebar */}
					<aside className="w-1/3 bg-white p-6 sidebar-content">
						{/* Photo */}
						{resume.showPhoto && resume.photo && (
							<div className="mb-6">
								<Image
									alt={fullName}
									className="w-full h-80 object-cover"
									height={320}
									src={resume.photo}
									width={240}
								/>
							</div>
						)}

						{/* Profiles */}
						{resume.profiles && resume.profiles.length > 0 && (
							<PikachuSidebarSection title={sectionTitles.profiles}>
								<SocialProfile
									className="space-y-2"
									layout="vertical"
									profiles={resume.profiles}
									showNetworkLabel={true}
								/>
							</PikachuSidebarSection>
						)}

						{/* Skills */}
						{resume.skills && resume.skills.length > 0 && (
							<PikachuSidebarSection title={sectionTitles.skills}>
								<SkillsSection
									className="[&_h4]:font-bold [&_h4]:text-gray-900 [&_h4]:mb-1 [&_h4]:text-sm [&_p]:text-gray-700 [&_p]:text-sm [&_p]:leading-relaxed"
									skills={resume.skills}
									layout="list"
								/>
								<div className="mt-2">
									<p className="text-gray-600 text-sm">Advanced Level</p>
								</div>
							</PikachuSidebarSection>
						)}

						{/* Certifications */}
						{resume.certifications && resume.certifications.length > 0 && (
							<PikachuSidebarSection title={sectionTitles.certifications}>
								<div className="space-y-3">
									{resume.certifications.map((cert) => (
										<CertificationItem
											key={cert.id}
											certification={cert}
											className="[&_h3]:font-bold [&_h3]:text-gray-900 [&_h3]:text-sm [&_p]:text-gray-700 [&_p]:text-sm"
											showCredentialId={false}
										/>
									))}
								</div>
							</PikachuSidebarSection>
						)}

						{/* Languages */}
						{resume.languages && resume.languages.length > 0 && (
							<PikachuSidebarSection title={sectionTitles.languages}>
								<div className="space-y-2">
									{resume.languages.map((lang) => (
										<LanguageItem
											key={lang.id}
											className="[&>span:first-child]:font-bold [&>span:first-child]:text-gray-900 [&>span:first-child]:text-sm [&>span:last-child]:text-gray-600 [&>span:last-child]:text-sm"
											language={lang}
											showBars={false}
											showLevel={true}
										/>
									))}
								</div>
							</PikachuSidebarSection>
						)}

						{/* References */}
						<PikachuSidebarSection title={sectionTitles.references}>
							<p className="text-gray-700 text-sm">Available upon request</p>
						</PikachuSidebarSection>
					</aside>

					{/* Right Column - Orange Header and Main Content */}
					<main className="w-2/3">
						{/* Orange Header */}
						<header className="bg-orange-500 text-white p-6 resume-header">
							<h1 className="text-3xl font-bold mb-2">{fullName}</h1>
							<p className="text-xl mb-4">{resume.jobTitle}</p>

							{/* Contact Information */}
							<ContactInfo
								className="text-white text-sm"
								email={resume.email}
								location={location}
								phone={resume.phone}
								variant="vertical"
								website={resume.website}
							/>
						</header>

						{/* Main Content Sections */}
						<div className="p-6">
							{/* Summary */}
							{resume.bio && (
								<PikachuMainSection title={sectionTitles.summary}>
									<ProfessionalSummary
										bio={resume.bio}
										className="text-sm text-gray-700 leading-relaxed"
										variant="paragraph"
									/>
								</PikachuMainSection>
							)}

							{/* Experience */}
							{resume.experiences && resume.experiences.length > 0 && (
								<PikachuMainSection title={sectionTitles.experience}>
									<div className="space-y-6">
										{resume.experiences.map((exp) => (
											<ExperienceItem
												key={exp.id}
												className="[&_.experience-website]:text-orange-600"
												experience={exp}
												locale={locale}
												showWebsiteIcon={true}
												variant="standard"
											/>
										))}
									</div>
								</PikachuMainSection>
							)}

							{/* Education */}
							{resume.educations && resume.educations.length > 0 && (
								<PikachuMainSection title={sectionTitles.education}>
									<div className="space-y-4">
										{resume.educations.map((edu) => (
											<EducationItem
												key={edu.id}
												education={edu}
												locale={locale}
												variant="standard"
											/>
										))}
									</div>
								</PikachuMainSection>
							)}

							{/* Projects */}
							{resume.projects && resume.projects.length > 0 && (
								<PikachuMainSection title={sectionTitles.projects}>
									<div className="space-y-4">
										{resume.projects.map((project) => (
											<ProjectItem
												key={project.id}
												project={project}
												showClient={true}
												showTechnologies={false}
												variant="standard"
											/>
										))}
									</div>
								</PikachuMainSection>
							)}
						</div>
					</main>
				</div>
			</div>
		</div>
	);
};

export default PikachuTemplate;
