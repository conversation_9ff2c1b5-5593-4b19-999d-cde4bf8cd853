import Image from "next/image";
import React from "react";
import {
	CertificationItem,
	ContactInfo,
	EducationItem,
	ExperienceItem,
	formatLocation,
	getFullName,
	LanguageItem,
	ProfessionalSummary,
	ProjectItem,
	SkillsSection,
	SocialProfile,
	TemplateProps,
	useSectionTranslations,
	useTemplateLocale,
} from "./base-components";

// Custom sidebar section for azurill template
const AzurillSidebarSection: React.FC<{
	title: string;
	children: React.ReactNode;
	accent?: boolean;
}> = ({ title, children, accent = false }) => (
	<section className="sidebar-section mb-6">
		<h2
			className={`section-title text-sm font-bold mb-3 flex items-center ${
				accent ? "text-orange-500" : "text-gray-700"
			}`}
		>
			{accent && <span className="w-2 h-2 bg-orange-500 rounded-full me-2" />}
			{title}
			{accent && <span className="w-2 h-2 bg-orange-500 rounded-full ms-2" />}
		</h2>
		{children}
	</section>
);

// Custom main section for azurill template
const AzurillMainSection: React.FC<{
	title: string;
	children: React.ReactNode;
	accent?: boolean;
}> = ({ title, children, accent = false }) => (
	<section className="resume-section mb-8">
		<h2
			className={`section-title text-lg font-bold mb-4 flex items-center ${
				accent ? "text-orange-500" : "text-gray-800"
			}`}
		>
			{accent && <span className="w-3 h-3 bg-orange-500 rounded-full me-3" />}
			{title}
		</h2>
		{children}
	</section>
);

/**
 * Azurill Resume Template - Two-Column Layout with Accent Colors
 * - Centered header with photo and contact info
 * - Left main content area for primary sections
 * - Right sidebar for skills, certifications, projects
 * - Orange accent colors for visual interest
 * - ATS-friendly structure with proper hierarchy
 */
export const AzurillTemplate: React.FC<TemplateProps> = ({
	resume,
	className = "",
}) => {
	const locale = useTemplateLocale();
	const sectionTitles = useSectionTranslations();
	const fullName = getFullName(resume.firstName, resume.lastName, locale);
	const location = formatLocation(resume.city, resume.country);

	return (
		<div
			className={`azurill-template bg-white text-gray-900 font-sans ${className}`}
		>
			{/* Page wrapper with A4 proportions */}
			<div className="min-h-[297mm] w-full min-w-[210mm] mx-auto bg-white p-8">
				{/* Header - Centered */}
				<header className="resume-header text-center mb-8 pb-6 border-b-2 border-gray-200">
					{/* Photo */}
					{resume.showPhoto && resume.photo && (
						<div className="mb-4">
							<Image
								alt={fullName}
								className="w-24 h-32 object-cover border border-gray-300 mx-auto rounded"
								height={128}
								src={resume.photo}
								width={96}
							/>
						</div>
					)}

					{/* Name and Title */}
					<h1 className="text-3xl font-bold text-gray-900 mb-2">{fullName}</h1>
					<p className="text-lg text-gray-600 font-medium mb-4">
						{resume.jobTitle}
					</p>

					{/* Contact Info */}
					<ContactInfo
						className="flex justify-center items-center flex-wrap gap-x-6 gap-y-2 text-sm text-gray-600 [&_span:first-child]:text-orange-500"
						email={resume.email}
						location={location}
						phone={resume.phone}
						variant="horizontal"
						website={resume.website}
					/>
				</header>

				{/* Two-Column Layout */}
				<div className="flex gap-8">
					{/* Left Sidebar */}
					<aside className="sidebar-content w-1/3">
						{/* Profiles */}
						{resume.profiles && resume.profiles.length > 0 && (
							<AzurillSidebarSection accent title={sectionTitles.profiles}>
								<SocialProfile
									className="space-y-2 [&>div]:mb-2"
									layout="vertical"
									profiles={resume.profiles}
									showNetworkLabel={true}
								/>
							</AzurillSidebarSection>
						)}

						{/* Skills */}
						{resume.skills && resume.skills.length > 0 && (
							<AzurillSidebarSection accent title={sectionTitles.skills}>
								<SkillsSection
									className="[&_h4]:text-sm [&_h4]:text-gray-800 [&_p]:text-xs [&_p]:text-gray-700"
									layout="list"
									skills={resume.skills}
								/>
							</AzurillSidebarSection>
						)}

						{/* Certifications */}
						{resume.certifications && resume.certifications.length > 0 && (
							<AzurillSidebarSection
								accent
								title={sectionTitles.certifications}
							>
								<div className="space-y-3">
									{resume.certifications.map((cert) => (
										<CertificationItem
											key={cert.id}
											certification={cert}
											className="text-center [&_h3]:text-sm [&_p]:text-xs"
										/>
									))}
								</div>
							</AzurillSidebarSection>
						)}
					</aside>

					{/* Main Content */}
					<main className="w-2/3">
						{/* Summary */}
						{resume.bio && (
							<AzurillMainSection accent title={sectionTitles.summary}>
								<ProfessionalSummary bio={resume.bio} variant="paragraph" />
							</AzurillMainSection>
						)}

						{/* Experience */}
						{resume.experiences && resume.experiences.length > 0 && (
							<AzurillMainSection accent title={sectionTitles.experience}>
								<div className="space-y-6">
									{resume.experiences.map((exp) => (
										<ExperienceItem
											key={exp.id}
											className="[&_.experience-header]:flex [&_.experience-header]:items-center [&_.experience-header]:mb-1 [&_.experience-header::before]:content-[''] [&_.experience-header::before]:w-2 [&_.experience-header::before]:h-2 [&_.experience-header::before]:bg-orange-500 [&_.experience-header::before]:rounded-full [&_.experience-header::before]me-3 [&_.experience-company]:font-bold [&_.experience-company]:text-gray-800 [&_.experience-title]:text-sm [&_.experience-title]:font-semibold [&_.experience-title]:text-gray-700 [&_.experience-title]:ms-5 [&_.experience-location]:text-xs [&_.experience-location]:text-gray-500 [&_.experience-location]:ms-5 [&_.experience-dates]:text-xs [&_.experience-dates]:font-bold [&_.experience-dates]:text-gray-600 [&_.experience-dates]:ms-5 [&_.experience-dates]:mb-2 [&_.experience-description]:text-sm [&_.experience-description]:text-gray-700 [&_.experience-description]:leading-relaxed [&_.experience-description]:ms-5 [&_.experience-website]:text-orange-500 [&_.experience-website]:text-xs [&_.experience-website]:ms-5 [&_.experience-website]:mt-2"
											experience={exp}
											locale={locale}
											variant="standard"
										/>
									))}
								</div>
							</AzurillMainSection>
						)}

						{/* Education */}
						{resume.educations && resume.educations.length > 0 && (
							<AzurillMainSection accent title={sectionTitles.education}>
								<div className="space-y-4">
									{resume.educations.map((edu) => (
										<EducationItem
											key={edu.id}
											className="[&_.education-header]:flex [&_.education-header]:items-center [&_.education-header]:mb-1 [&_.education-header::before]:content-[''] [&_.education-header::before]:w-2 [&_.education-header::before]:h-2 [&_.education-header::before]:bg-orange-500 [&_.education-header::before]:rounded-full [&_.education-header::before]:me-3 [&_.education-institution]:font-bold [&_.education-institution]:text-gray-800 [&_.education-location]:text-sm [&_.education-location]:text-gray-700 [&_.education-location]:ms-5 [&_.education-degree]:text-sm [&_.education-degree]:font-medium [&_.education-degree]:text-gray-800 [&_.education-degree]:ms-5 [&_.education-dates]:text-xs [&_.education-dates]:font-bold [&_.education-dates]:text-gray-600 [&_.education-dates]:ms-5 [&_.education-description]:text-sm [&_.education-description]:text-gray-700 [&_.education-description]:leading-relaxed [&_.education-description]:ms-5 [&_.education-description]:mt-2"
											education={edu}
											locale={locale}
											variant="standard"
										/>
									))}
								</div>
							</AzurillMainSection>
						)}

						{/* Projects */}
						{resume.projects && resume.projects.length > 0 && (
							<AzurillMainSection accent title={sectionTitles.projects}>
								<div className="space-y-4">
									{resume.projects.map((project) => (
										<ProjectItem
											key={project.id}
											className="[&_.project-header]:flex [&_.project-header]:items-center [&_.project-header]:mb-1 [&_.project-header::before]:content-[''] [&_.project-header::before]:w-2 [&_.project-header::before]:h-2 [&_.project-header::before]:bg-orange-500 [&_.project-header::before]:rounded-full [&_.project-header::before]me-3 [&_.project-title]:font-bold [&_.project-title]:text-gray-800 [&_.project-client]:text-sm [&_.project-client]:text-gray-600 [&_.project-client]:ms-5 [&_.project-url]:text-orange-500 [&_.project-url]:text-xs [&_.project-url]:ms-5 [&_.project-url]:mt-1 [&_.project-description]:text-sm [&_.project-description]:text-gray-700 [&_.project-description]:leading-relaxed [&_.project-description]:ms-5 [&_.project-description]:mt-2"
											project={project}
											showClient={true}
											showTechnologies={false}
										/>
									))}
								</div>
							</AzurillMainSection>
						)}

						{/* Languages */}
						{resume.languages && resume.languages.length > 0 && (
							<AzurillMainSection accent title={sectionTitles.languages}>
								<div className="grid grid-cols-2 gap-4">
									{resume.languages.map((lang) => (
										<LanguageItem
											key={lang.id}
											className="flex justify-between [&_.language-name]:font-medium [&_.language-name]:text-gray-800 [&_.language-name]:text-sm [&_.language-level]:text-gray-600 [&_.language-level]:text-sm"
											language={lang}
											showBars={false}
											showLevel={true}
										/>
									))}
								</div>
							</AzurillMainSection>
						)}
					</main>
				</div>
			</div>
		</div>
	);
};

export default AzurillTemplate;
