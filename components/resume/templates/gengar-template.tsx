import Image from "next/image";
import React from "react";
import {
	CertificationItem,
	EducationItem,
	ExperienceItem,
	formatLocation,
	getFullName,
	ProfessionalSummary,
	ProjectItem,
	SkillsSection,
	SocialProfile,
	TemplateProps,
	useSectionTranslations,
	useTemplateLocale,
} from "./base-components";

// Custom main content section for gengar template
const GengarSection: React.FC<{
	title: string;
	children: React.ReactNode;
}> = ({ title, children }) => (
	<section className="resume-section mb-6">
		<h2 className="section-title text-lg font-bold mb-3 text-gray-900 border-b border-gray-400 pb-1">
			{title}
		</h2>
		{children}
	</section>
);

// Custom sidebar section for gengar template
const GengarSidebarSection: React.FC<{
	title: string;
	children: React.ReactNode;
}> = ({ title, children }) => (
	<section className="sidebar-section mb-6">
		<h3 className="section-title text-lg font-bold mb-3 text-white border-b border-teal-400 pb-1">
			{title}
		</h3>
		{children}
	</section>
);

/**
 * Gengar Resume Template - Clean Two-Column Layout
 * - Teal sidebar on left with photo, contact, and secondary info
 * - White main content area on right for primary sections
 * - Clean typography and professional appearance
 * - Professional color scheme matching reference design
 * - ATS-friendly structure with proper hierarchy
 */
export const GengarTemplate: React.FC<TemplateProps> = ({
	resume,
	className = "",
}) => {
	const locale = useTemplateLocale();
	const sectionTitles = useSectionTranslations();
	const fullName = getFullName(resume.firstName, resume.lastName, locale);
	const location = formatLocation(resume.city, resume.country);

	return (
		<div
			className={`gengar-template bg-white text-gray-900 font-sans ${className}`}
		>
			{/* Page wrapper with A4 proportions */}
			<div className="min-h-[297mm] w-full min-w-[210mm] mx-auto bg-white">
				{/* Two-Column Layout - Full Height */}
				<div className="flex min-h-[297mm]">
					{/* Left Sidebar - Teal Background */}
					<aside className="sidebar-content w-1/3 bg-teal-500 text-white p-6">
						{/* Photo */}
						{resume.showPhoto && resume.photo && (
							<div className="mb-6 text-center">
								<Image
									alt={fullName}
									className="w-32 h-40 object-cover mx-auto"
									height={160}
									src={resume.photo}
									width={128}
								/>
							</div>
						)}

						{/* Name and Title */}
						<div className="text-center mb-6">
							<h1 className="text-xl font-bold text-white mb-2">{fullName}</h1>
							<p className="text-sm text-teal-100">{resume.jobTitle}</p>
						</div>

						{/* Contact Information */}
						<div className="contact-section mb-6">
							<div className="space-y-3 text-sm text-white">
								{location && (
									<div className="flex items-start">
										<span className="text-teal-200 me-2 mt-0.5">📍</span>
										<span>{location}</span>
									</div>
								)}
								{resume.phone && (
									<div className="flex items-start">
										<span className="text-teal-200 me-2 mt-0.5">📞</span>
										<span>{resume.phone}</span>
									</div>
								)}
								{resume.email && (
									<div className="flex items-start">
										<span className="text-teal-200 me-2 mt-0.5">✉️</span>
										<span className="break-all">{resume.email}</span>
									</div>
								)}
								{resume.website && (
									<div className="flex items-start">
										<span className="text-teal-200 me-2 mt-0.5">🔗</span>
										<span className="break-all">{resume.website}</span>
									</div>
								)}
							</div>
						</div>

						{/* Profiles */}
						{resume.profiles && resume.profiles.length > 0 && (
							<GengarSidebarSection title={sectionTitles.profiles}>
								<SocialProfile
									className="[&_span]:text-white [&_div]:text-white [&_div]:text-sm"
									layout="vertical"
									profiles={resume.profiles}
									showNetworkLabel={true}
								/>
							</GengarSidebarSection>
						)}

						{/* Skills */}
						{resume.skills && resume.skills.length > 0 && (
							<GengarSidebarSection title={sectionTitles.skills}>
								<SkillsSection
									className="[&_h4]:text-white [&_h4]:font-bold [&_h4]:text-sm [&_p]:text-teal-100 [&_p]:text-sm"
									layout="list"
									skills={resume.skills}
								/>
							</GengarSidebarSection>
						)}

						{/* Certifications */}
						{resume.certifications && resume.certifications.length > 0 && (
							<GengarSidebarSection title={sectionTitles.certifications}>
								<div className="space-y-3">
									{resume.certifications.map((cert) => (
										<CertificationItem
											key={cert.id}
											certification={cert}
											className="[&_h3]:text-white [&_h3]:text-sm [&_p]:text-teal-100 [&_p]:text-sm"
											showCredentialId={false}
										/>
									))}
								</div>
							</GengarSidebarSection>
						)}
					</aside>

					{/* Right Column - Main Content */}
					<main className="w-2/3 p-6">
						{/* Summary */}
						{resume.bio && (
							<GengarSection title={sectionTitles.summary}>
								<ProfessionalSummary bio={resume.bio} variant="paragraph" />
							</GengarSection>
						)}

						{/* Experience */}
						{resume.experiences && resume.experiences.length > 0 && (
							<GengarSection title={sectionTitles.experience}>
								<div className="space-y-6">
									{resume.experiences.map((exp) => (
										<ExperienceItem
											key={exp.id}
											experience={exp}
											locale={locale}
											showWebsiteIcon={true}
											variant="standard"
										/>
									))}
								</div>
							</GengarSection>
						)}

						{/* Education */}
						{resume.educations && resume.educations.length > 0 && (
							<GengarSection title={sectionTitles.education}>
								<div className="space-y-4">
									{resume.educations.map((edu) => (
										<EducationItem
											key={edu.id}
											education={edu}
											locale={locale}
											variant="standard"
										/>
									))}
								</div>
							</GengarSection>
						)}

						{/* Projects */}
						{resume.projects && resume.projects.length > 0 && (
							<GengarSection title={sectionTitles.projects}>
								<div className="space-y-4">
									{resume.projects.map((project) => (
										<ProjectItem
											key={project.id}
											project={project}
											showClient={true}
											showTechnologies={false}
											variant="standard"
										/>
									))}
								</div>
							</GengarSection>
						)}
					</main>
				</div>
			</div>
		</div>
	);
};

export default GengarTemplate;
