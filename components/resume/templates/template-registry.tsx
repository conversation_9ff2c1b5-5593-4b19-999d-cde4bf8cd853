"use client";
import React, { useMemo } from "react";
import { Template } from "@/db/schema";
import { FullResume } from "@/db/schema";
import AzurillTemplate from "./azurill-template";
import BronzorTemplate from "./bronzor-template";
import { ChikoritaTemplate } from "./chikorita-template";
import DittoTemplate from "./ditto-template";
import GengarTemplate from "./gengar-template";
import GlalieTemplate from "./glalie-template";
import KakunaTemplate from "./kakuna-template";
import LeafishTemplate from "./leafish-template";
import NosepassTemplate from "./nosepass-template";
import OnyxTemplate from "./onyx-template";
import PikachuTemplate from "./pikachu-template";
import RhyhornTemplate from "./rhyhorn-template";

interface TemplateRendererProps {
	resume: FullResume;
	className?: string;
	templates: Template[];
}

// Template renderer component
export const TemplateRenderer = React.forwardRef<
	HTMLDivElement,
	TemplateRendererProps
>(({ resume, className, templates }) => {
	const template = useMemo(() => {
		return (
			templates.find((template) => template.id === resume.template_id) ||
			templates[0]
		);
	}, [resume.template_id, templates]);

	if (!template) {
		return <div className="error">No templates available</div>;
	}

	const TemplateComponent = () => {
		switch (template.slug) {
			case "azurill":
				return <AzurillTemplate className={className} resume={resume} />;
			case "bronzor":
				return <BronzorTemplate className={className} resume={resume} />;
			case "chikorita":
				return <ChikoritaTemplate className={className} resume={resume} />;
			case "ditto":
				return <DittoTemplate className={className} resume={resume} />;
			case "gengar":
				return <GengarTemplate className={className} resume={resume} />;
			case "glalie":
				return <GlalieTemplate className={className} resume={resume} />;
			case "kakuna":
				return <KakunaTemplate className={className} resume={resume} />;
			case "leafish":
				return <LeafishTemplate className={className} resume={resume} />;
			case "nosepass":
				return <NosepassTemplate className={className} resume={resume} />;
			case "onyx":
				return <OnyxTemplate className={className} resume={resume} />;
			case "pikachu":
				return <PikachuTemplate className={className} resume={resume} />;
			case "rhyhorn":
				return <RhyhornTemplate className={className} resume={resume} />;
			default:
				return <BronzorTemplate className={className} resume={resume} />;
		}
	};
	return (
		// <div id="resume-preview">
		<TemplateComponent />
		// </div>
	);
});

TemplateRenderer.displayName = "TemplateRenderer";

export default TemplateRenderer;
