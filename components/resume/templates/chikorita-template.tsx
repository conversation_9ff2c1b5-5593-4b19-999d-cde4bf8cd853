import Image from "next/image";
import React from "react";
import {
	EducationItem,
	ExperienceItem,
	formatLocation,
	getFullName,
	LanguageItem,
	ProfessionalSummary,
	ProjectItem,
	ResumeSection,
	SkillsSection,
	SocialProfile,
	TemplateProps,
	useSectionTranslations,
	useTemplateLocale,
} from "./base-components";

// Custom sidebar section for chikorita template
const ChikoritaSidebarSection: React.FC<{
	title: string;
	children: React.ReactNode;
}> = ({ title, children }) => (
	<section className="mb-4 sidebar-section">
		<h3 className="text-lg font-bold mb-2 pb-1 border-b border-white text-white section-title">
			{title}
		</h3>
		{children}
	</section>
);

// Custom contact component with green icons
const ChikoritaContact: React.FC<{
	icon: string;
	children: React.ReactNode;
}> = ({ icon, children }) => (
	<div className="flex items-center">
		<span className="text-green-600 me-2">{icon}</span>
		{children}
	</div>
);

/**
 * Chikorita Resume Template - Two-Column Layout with Green Sidebar
 * - Photo and contact info header
 * - Left column for main sections (Profiles, Summary, Experience, Education, Projects)
 * - Right green sidebar for Skills, Certifications, Languages, References
 * - Clean typography matching the reference design
 * - ATS-friendly structure with proper hierarchy
 */
export const ChikoritaTemplate: React.FC<TemplateProps> = ({
	resume,
	className = "",
}) => {
	const locale = useTemplateLocale();
	const sectionTitles = useSectionTranslations();
	const fullName = getFullName(resume.firstName, resume.lastName, locale);
	const location = formatLocation(resume.city, resume.country);

	return (
		<div
			className={`chikorita-template text-gray-900 font-sans w-full ${className}`}
		>
			{/* Page wrapper with A4 proportions */}
			<div className="h-full w-full">
				{/* Two-Column Layout - Full Height */}
				<div className="flex h-full">
					{/* Left Column - Main Content */}
					<main className="w-2/3 bg-white flex flex-col">
						{/* Header with Photo and Contact Info */}
						<header className="p-4 pb-3 resume-header flex-shrink-0">
							<div className="flex items-start gap-6">
								{/* Photo */}
								{resume.showPhoto && resume.photo && (
									<div className="flex-shrink-0">
										<Image
											alt={fullName}
											className="w-24 h-32 object-cover border border-gray-300"
											height={128}
											src={resume.photo}
											width={96}
										/>
									</div>
								)}

								{/* Name, Title and Contact Info */}
								<div className="flex-1">
									<h1 className="text-2xl font-bold text-gray-900 mb-1">
										{fullName}
									</h1>
									<p className="text-lg text-gray-700 mb-4">
										{resume.jobTitle}
									</p>

									{/* Contact Information with green icons */}
									<div className="space-y-1 text-sm text-gray-700 contact-section">
										{location && (
											<ChikoritaContact icon="📍">
												<span>{location}</span>
											</ChikoritaContact>
										)}
										{resume.phone && (
											<ChikoritaContact icon="📞">
												<span>{resume.phone}</span>
											</ChikoritaContact>
										)}
										{resume.email && (
											<ChikoritaContact icon="✉️">
												<span>{resume.email}</span>
											</ChikoritaContact>
										)}
										{resume.website && (
											<ChikoritaContact icon="🔗">
												<span>{resume.website}</span>
											</ChikoritaContact>
										)}
									</div>
								</div>
							</div>
						</header>

						{/* Main Content Sections */}
						<div className="px-4 flex-1 flex flex-col justify-start">
							{/* Profiles */}
							{resume.profiles && resume.profiles.length > 0 && (
								<ResumeSection title={sectionTitles.profiles} variant="default">
									<SocialProfile
										className="grid-cols-3 gap-4"
										layout="grid"
										profiles={resume.profiles}
										showNetworkLabel={true}
									/>
								</ResumeSection>
							)}

							{/* Summary */}
							{resume.bio && (
								<ResumeSection title={sectionTitles.summary} variant="default">
									<ProfessionalSummary bio={resume.bio} variant="paragraph" />
								</ResumeSection>
							)}

							{/* Experience */}
							{resume.experiences && resume.experiences.length > 0 && (
								<ResumeSection
									title={sectionTitles.experience}
									variant="default"
								>
									<div className="space-y-4">
										{resume.experiences.map((exp) => (
											<ExperienceItem
												key={exp.id}
												experience={exp}
												locale={locale}
												showWebsiteIcon={true}
												variant="standard"
											/>
										))}
									</div>
								</ResumeSection>
							)}

							{/* Education */}
							{resume.educations && resume.educations.length > 0 && (
								<ResumeSection
									title={sectionTitles.education}
									variant="default"
								>
									<div className="space-y-4">
										{resume.educations.map((edu) => (
											<EducationItem
												key={edu.id}
												education={edu}
												locale={locale}
												variant="standard"
											/>
										))}
									</div>
								</ResumeSection>
							)}

							{/* Projects */}
							{resume.projects && resume.projects.length > 0 && (
								<ResumeSection title={sectionTitles.projects} variant="default">
									<div className="grid grid-cols-2 gap-6">
										{resume.projects.map((project) => (
											<ProjectItem
												key={project.id}
												project={project}
												showClient={true}
												showTechnologies={false}
												variant="standard"
											/>
										))}
									</div>
								</ResumeSection>
							)}
						</div>
					</main>

					{/* Right Column - Green Sidebar */}
					<aside className="w-1/3 bg-green-600 text-white p-4 sidebar-content h-full">
						{/* Skills */}
						{resume.skills && resume.skills.length > 0 && (
							<ChikoritaSidebarSection title={sectionTitles.skills}>
								<div className="text-white">
									<SkillsSection
										className="[&_h4]:text-white [&_h4]:font-bold [&_p]:text-white [&_p]:text-sm"
										layout="list"
										skills={resume.skills}
									/>
								</div>
							</ChikoritaSidebarSection>
						)}

						{/* Certifications */}
						{resume.certifications && resume.certifications.length > 0 && (
							<ChikoritaSidebarSection title={sectionTitles.certifications}>
								<div className="space-y-3">
									{resume.certifications.map((cert) => (
										<div key={cert.id} className="text-white">
											<h4 className="font-bold text-white text-sm">
												{cert.title}
											</h4>
											<p className="text-white text-sm">{cert.issuer}</p>
											{cert.dateReceived && (
												<p className="text-white text-sm">
													{cert.dateReceived}
												</p>
											)}
										</div>
									))}
								</div>
							</ChikoritaSidebarSection>
						)}

						{/* Languages */}
						{resume.languages && resume.languages.length > 0 && (
							<ChikoritaSidebarSection title={sectionTitles.languages}>
								<div className="space-y-3">
									{resume.languages.map((lang) => (
										<div key={lang.id} className="text-white">
											<LanguageItem
												className="[&_span]:text-white"
												language={lang}
												showBars={false}
												showLevel={true}
											/>
										</div>
									))}
								</div>
							</ChikoritaSidebarSection>
						)}

						{/* References */}
						<ChikoritaSidebarSection title={sectionTitles.references}>
							<p className="text-white text-sm">Available upon request</p>
						</ChikoritaSidebarSection>
					</aside>
				</div>
			</div>
		</div>
	);
};
