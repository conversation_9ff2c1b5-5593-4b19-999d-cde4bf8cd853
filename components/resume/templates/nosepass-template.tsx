import Image from "next/image";
import React from "react";
import { Language } from "@/db/schema";

import {
	CertificationItem,
	ContactInfo,
	EducationItem,
	ExperienceItem,
	formatLocation,
	getFullName,
	ProfessionalSummary,
	ProjectItem,
	SkillsSection,
	SocialProfile,
	TemplateProps,
	useSectionTranslations,
	useTemplateLocale,
} from "./base-components";

// Section with title and horizontal line - Custom Europass styling
const EuropassSection: React.FC<{
	title: string;
	children: React.ReactNode;
}> = ({ title, children }) => (
	<div className="resume-section mb-8">
		<div className="flex items-center mb-4">
			<h2 className="section-title w-1/3 text-lg font-semibold text-blue-400 pr-4">
				{title}
			</h2>
			<div className="w-2/3 h-0.5 bg-blue-400" />
		</div>
		{children}
	</div>
);

// Custom wrapper for Europass-style layout
const EuropassContentWrapper: React.FC<{
	children: React.ReactNode;
}> = ({ children }) => (
	<div className="flex">
		<div className="w-1/3" /> {/* Empty space for alignment */}
		<div className="w-2/3">{children}</div>
	</div>
);

// Custom Europass-style language component with dots
const EuropassLanguageItem: React.FC<{
	language: Language;
	locale: string;
}> = ({ language }) => (
	<div className="flex justify-between items-center">
		<span className="text-sm font-medium text-gray-800">{language.name}</span>
		<div className="flex space-x-1">
			{[...Array(5)].map((_, i) => (
				<div
					key={i}
					className={`w-2 h-2 rounded-full ${
						i < language.proficiency / 20 ? "bg-blue-400" : "bg-gray-300"
					}`}
				/>
			))}
		</div>
	</div>
);

/**
 * Nosepass Resume Template - Europass Style
 * - Clean, professional Europass-inspired layout
 * - Each section has title with horizontal line
 * - Two-column layout within each section: dates on left, content on right
 * - ATS-friendly structure with semantic HTML
 */
export const NosepassTemplate: React.FC<TemplateProps> = ({
	resume,
	className = "",
}) => {
	const locale = useTemplateLocale();
	const sectionTitles = useSectionTranslations();
	const fullName = getFullName(resume.firstName, resume.lastName, locale);
	const location = formatLocation(resume.city, resume.country);

	return (
		<div
			className={`nosepass-template bg-white text-gray-900 font-sans ${className}`}
		>
			{/* Page wrapper with A4 proportions */}
			<div className="min-h-[297mm] w-full min-w-[210mm] mx-auto bg-white p-8">
				{/* Header */}
				<header className="resume-header text-center py-4 mb-4">
					<div className="flex justify-between items-center">
						<div className="flex items-center space-x-2">
							<span className="text-blue-500 text-2xl">🧭</span>
							<span className="text-sm text-gray-600 font-medium">
								europass
							</span>
						</div>
						<h1 className="text-xl font-normal text-gray-600">
							Curriculum Vitae
						</h1>
						<h2 className="text-xl font-semibold text-gray-800">{fullName}</h2>
					</div>
				</header>

				{/* Personal Information Section */}
				<EuropassSection title="Personal Information">
					<div className="flex">
						<div className="w-1/3" /> {/* Empty space for alignment */}
						<div className="w-2/3">
							<div className="flex items-start space-x-6">
								{/* Photo */}
								{resume.showPhoto && resume.photo && (
									<div className="flex-shrink-0">
										<Image
											alt={fullName}
											className="w-24 h-32 object-cover border border-gray-300"
											height={128}
											src={resume.photo}
											width={96}
										/>
									</div>
								)}

								{/* Name, title and contact info */}
								<div className="flex-1">
									{/* Name and Job Title */}
									<div className="mb-4">
										<h3 className="text-2xl font-bold text-gray-900 mb-1">
											{fullName}
										</h3>
										<p className="text-lg text-gray-700 font-medium">
											{resume.jobTitle}
										</p>
									</div>

									{/* Contact Information */}
									<ContactInfo
										className="space-y-1 text-sm [&_span]:text-gray-700 [&_a]:text-blue-500"
										email={resume.email}
										location={location}
										phone={resume.phone}
										variant="vertical"
										website={resume.website}
									/>
								</div>
							</div>
						</div>
					</div>
				</EuropassSection>

				{/* Profiles Section */}
				{resume.profiles && resume.profiles.length > 0 && (
					<EuropassSection title={sectionTitles.profiles}>
						<EuropassContentWrapper>
							<SocialProfile
								className="space-y-3 [&_.flex]:space-x-3 [&_span:first-child]:text-blue-500 [&_.font-medium]:text-gray-800 [&_.text-gray-600]:text-xs [&_.text-gray-600]:block [&_.text-gray-600]:mt-0.5"
								layout="vertical"
								profiles={resume.profiles}
								showNetworkLabel={false}
							/>
						</EuropassContentWrapper>
					</EuropassSection>
				)}

				{/* Summary Section */}
				{resume.bio && (
					<EuropassSection title={sectionTitles.summary}>
						<EuropassContentWrapper>
							<ProfessionalSummary
								bio={resume.bio}
								className="text-sm text-gray-700 leading-relaxed"
								variant="paragraph"
							/>
						</EuropassContentWrapper>
					</EuropassSection>
				)}

				{/* Experience Section */}
				{resume.experiences && resume.experiences.length > 0 && (
					<EuropassSection title={sectionTitles.experience}>
						<div className="space-y-6">
							{resume.experiences.map((exp) => (
								<ExperienceItem
									key={exp.id}
									className="[&_.font-bold]:text-base [&_.font-bold]:mb-1 [&_.text-gray-700]:font-semibold [&_.text-gray-700]:text-gray-800 [&_.text-gray-700]:mb-1 [&_.text-gray-600]:mb-2"
									experience={exp}
									locale={locale}
									showWebsiteIcon={false}
									variant="europass"
								/>
							))}
						</div>
					</EuropassSection>
				)}

				{/* Education Section */}
				{resume.educations && resume.educations.length > 0 && (
					<EuropassSection title={sectionTitles.education}>
						<div className="space-y-4">
							{resume.educations.map((edu) => (
								<EducationItem
									key={edu.id}
									className="[&_.font-bold]:text-base [&_.text-gray-700]:font-medium [&_.text-gray-700]:text-gray-800 [&_.text-gray-700]:order-last [&_.text-gray-600]:text-sm [&_.text-gray-600]:order-first"
									education={edu}
									locale={locale}
									variant="europass"
								/>
							))}
						</div>
					</EuropassSection>
				)}

				{/* Skills Section */}
				{resume.skills && resume.skills.length > 0 && (
					<EuropassSection title={sectionTitles.skills}>
						<EuropassContentWrapper>
							<div className="grid grid-cols-2 gap-4">
								<SkillsSection
									className="col-span-2 [&_.skills-section]:space-y-0 [&_.mb-3]:mb-2 [&_.font-semibold]:text-sm [&_.text-sm]:text-sm"
									skills={resume.skills}
								/>
							</div>
						</EuropassContentWrapper>
					</EuropassSection>
				)}

				{/* Projects Section */}
				{resume.projects && resume.projects.length > 0 && (
					<EuropassSection title={sectionTitles.projects}>
						<div className="space-y-4">
							{resume.projects.map((project) => (
								<div key={project.id} className="project-item mb-4 flex">
									{/* Date column - custom for Europass layout */}
									<div className="w-1/3 text-sm text-blue-500 font-medium pr-4">
										{project.startDate &&
											project.endDate &&
											project.startDate + " - " + project.endDate}
									</div>
									{/* Content column */}
									<div className="w-2/3">
										<ProjectItem
											className="[&_.flex]:block [&_.text-right]:hidden [&_.font-bold]:text-base [&_.font-bold]:mb-0 [&_.text-gray-700]:mb-1 [&_.text-blue-500]:mb-2"
											project={project}
											showClient={true}
											showTechnologies={false}
											variant="standard"
										/>
									</div>
								</div>
							))}
						</div>
					</EuropassSection>
				)}

				{/* Languages Section */}
				{resume.languages && resume.languages.length > 0 && (
					<EuropassSection title={sectionTitles.languages}>
						<EuropassContentWrapper>
							<div className="space-y-2">
								{resume.languages.map((lang) => (
									<EuropassLanguageItem
										key={lang.id}
										language={lang}
										locale={locale}
									/>
								))}
							</div>
						</EuropassContentWrapper>
					</EuropassSection>
				)}

				{/* Certifications Section */}
				{resume.certifications && resume.certifications.length > 0 && (
					<EuropassSection title={sectionTitles.certifications}>
						<div className="space-y-3">
							{resume.certifications.map((cert) => (
								<div key={cert.id} className="certification-item flex">
									{/* Date column */}
									<div className="w-1/3 text-sm text-blue-500 font-medium pr-4">
										{cert.dateReceived && cert.dateReceived}
									</div>
									{/* Content column */}
									<div className="w-2/3">
										<CertificationItem
											certification={cert}
											className="[&_.flex]:block [&_.text-right]:hidden [&_.font-bold]:font-semibold [&_.font-bold]:text-sm [&_.text-gray-700]:text-gray-600"
											showCredentialId={false}
										/>
									</div>
								</div>
							))}
						</div>
					</EuropassSection>
				)}
			</div>
		</div>
	);
};

export default NosepassTemplate;
