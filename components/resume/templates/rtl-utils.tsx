"use client";

import { useLocale } from "next-intl";

export const useRTL = () => {
	const locale = useLocale();
	return locale === "ar";
};

export const RTLWrapper: React.FC<{
	children: React.ReactNode;
	className?: string;
}> = ({ children, className = "" }) => {
	const isRTL = useRTL();

	return (
		<div className={`${className}`} dir={isRTL ? "rtl" : "ltr"}>
			{children}
		</div>
	);
};

// Template section wrapper that handles RTL using tailwindcss-rtl
export const RTLSection: React.FC<{
	title: string;
	children: React.ReactNode;
	className?: string;
}> = ({ title, children, className = "" }) => {
	return (
		<section className={`mb-6 ${className}`}>
			<h2 className="text-lg font-bold mb-3 text-gray-900 border-b border-orange-400 pb-1 text-start">
				{title}
			</h2>
			<div className="text-start">{children}</div>
		</section>
	);
};

// RTL-aware contact info component using tailwindcss-rtl
export const RTLContactInfo: React.FC<{
	icon: React.ReactNode;
	text: string;
	className?: string;
}> = ({ icon, text, className = "" }) => {
	return (
		<div className={`flex items-center mb-1 ${className}`}>
			<span className="me-2">{icon}</span>
			<span className="text-sm contact-info">{text}</span>
		</div>
	);
};
