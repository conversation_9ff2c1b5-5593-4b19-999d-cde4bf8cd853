import Image from "next/image";
import React from "react";
import {
	CertificationItem,
	ContactInfo,
	EducationItem,
	ExperienceItem,
	formatLocation,
	getFullName,
	ProfessionalSummary,
	ProjectItem,
	ResumeSection,
	SkillsSection,
	SocialProfile,
	TemplateProps,
	useSectionTranslations,
	useTemplateLocale,
} from "./base-components";

// Custom Section component with Leafish template styling
const LeafishSection: React.FC<{
	title: string;
	children: React.ReactNode;
}> = ({ title, children }) => (
	<ResumeSection className="leafish-section" title={title} variant="default">
		{children}
	</ResumeSection>
);

/**
 * Leafish Resume Template - Clean Layout with Header and Two Columns
 * - Header section with name, title, summary on left and photo on right
 * - Contact info and social links below header
 * - Two-column layout: Experience/Education on left, Skills/Certifications/Projects on right
 * - Clean typography and minimal styling
 * - Professional appearance matching reference design
 * - ATS-friendly structure with proper hierarchy
 */
export const LeafishTemplate: React.FC<TemplateProps> = ({
	resume,
	className = "",
}) => {
	const locale = useTemplateLocale();
	const sectionTitles = useSectionTranslations();
	const fullName = getFullName(resume.firstName, resume.lastName, locale);
	const location = formatLocation(resume.city, resume.country);

	return (
		<div
			className={`leafish-template bg-gray-100 text-gray-900 font-sans ${className}`}
		>
			{/* Page wrapper with A4 proportions */}
			<div className="min-h-[297mm] w-full min-w-[210mm] mx-auto bg-gray-100 p-8">
				{/* Header Section */}
				<header className="resume-header mb-6">
					<div className="flex justify-between items-start mb-4">
						{/* Left side: Name, Title, Summary */}
						<div className="flex-1 pr-6">
							<h1 className="text-2xl font-bold text-gray-900 mb-1">
								{fullName}
							</h1>
							<p className="text-lg text-gray-700 mb-4">{resume.jobTitle}</p>

							{/* Summary */}
							{resume.bio && (
								<ProfessionalSummary
									bio={resume.bio}
									className="text-sm text-gray-700 leading-relaxed"
									variant="paragraph"
								/>
							)}
						</div>

						{/* Right side: Photo */}
						{resume.showPhoto && resume.photo && (
							<div className="flex-shrink-0">
								<Image
									alt={fullName}
									className="w-32 h-40 object-cover"
									height={160}
									src={resume.photo}
									width={128}
								/>
							</div>
						)}
					</div>

					{/* Contact Information */}
					<ContactInfo
						className="text-sm text-gray-700 mb-4"
						email={resume.email}
						location={location}
						phone={resume.phone}
						variant="horizontal"
						website={resume.website}
					/>

					{/* Social Media Links */}
					{resume.profiles && resume.profiles.length > 0 && (
						<SocialProfile
							className="text-blue-600"
							layout="horizontal"
							profiles={resume.profiles}
							showNetworkLabel={false}
						/>
					)}
				</header>

				{/* Two-Column Layout */}
				<div className="grid grid-cols-1 md:grid-cols-2 gap-8">
					{/* Left Column - Experience and Education */}
					<div>
						{/* Experience */}
						{resume.experiences && resume.experiences.length > 0 && (
							<LeafishSection title={sectionTitles.experience}>
								<div className="space-y-6">
									{resume.experiences.map((exp) => (
										<ExperienceItem
											key={exp.id}
											className="leafish-experience-item"
											experience={exp}
											locale={locale}
											showWebsiteIcon={true}
											variant="standard"
										/>
									))}
								</div>
							</LeafishSection>
						)}

						{/* Education */}
						{resume.educations && resume.educations.length > 0 && (
							<LeafishSection title={sectionTitles.education}>
								<div className="space-y-4">
									{resume.educations.map((edu) => (
										<EducationItem
											key={edu.id}
											className="leafish-education-item"
											education={edu}
											locale={locale}
											variant="standard"
										/>
									))}
								</div>
							</LeafishSection>
						)}
					</div>

					{/* Right Column - Skills, Certifications, Projects */}
					<div>
						{/* Skills */}
						{resume.skills && resume.skills.length > 0 && (
							<LeafishSection title={sectionTitles.skills}>
								<SkillsSection
									className="leafish-skills-section"
									layout="list"
									skills={resume.skills}
								/>
							</LeafishSection>
						)}

						{/* Certifications */}
						{resume.certifications && resume.certifications.length > 0 && (
							<LeafishSection title={sectionTitles.certifications}>
								<div className="space-y-3">
									{resume.certifications.map((cert) => (
										<CertificationItem
											key={cert.id}
											certification={cert}
											className="leafish-certification-item"
											showCredentialId={false}
										/>
									))}
								</div>
							</LeafishSection>
						)}

						{/* Projects */}
						{resume.projects && resume.projects.length > 0 && (
							<LeafishSection title={sectionTitles.projects}>
								<div className="space-y-4">
									{resume.projects.map((project) => (
										<ProjectItem
											key={project.id}
											className="leafish-project-item"
											project={project}
											showClient={true}
											showTechnologies={false}
											variant="standard"
										/>
									))}
								</div>
							</LeafishSection>
						)}
					</div>
				</div>
			</div>
		</div>
	);
};

export default LeafishTemplate;
