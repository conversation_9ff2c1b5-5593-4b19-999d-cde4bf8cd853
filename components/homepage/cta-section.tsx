"use client";
import { <PERSON><PERSON>, <PERSON>, CardBody } from "@heroui/react";
import { useTranslations } from "next-intl";
import { subtitle, title } from "@/components/primitives";
import CreateResumeButton from "@/components/resume/createResumeButton";

export default function CTASection() {
	const t = useTranslations("homepage");

	return (
		<section className="py-20 md:py-32">
			<div className="max-w-4xl mx-auto px-4">
				<Card className="border-none shadow-2xl bg-gradient-to-r from-primary-600 to-secondary-600 text-white">
					<CardBody className="p-12 text-center">
						<h2 className={title({ size: "md", class: "text-white" })}>
							{t("cta.title")}
						</h2>
						<p className={subtitle({ class: "mt-4 text-white/90" })}>
							{t("cta.subtitle")}
						</p>

						<div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
							<CreateResumeButton
								className="bg-white text-primary-600 hover:bg-white/90 font-semibold"
								size="lg"
							/>
							<Button
								as="a"
								className="border-white text-white hover:bg-white/10 font-semibold"
								href="/templates"
								size="lg"
								variant="bordered"
							>
								{t("cta.explore_templates")}
							</Button>
						</div>

						<div className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-6 text-sm text-white/80">
							<div className="flex items-center gap-2">
								<span>✓</span>
								<span>{t("cta.benefit_1")}</span>
							</div>
							<div className="flex items-center gap-2">
								<span>✓</span>
								<span>{t("cta.benefit_2")}</span>
							</div>
							<div className="flex items-center gap-2">
								<span>✓</span>
								<span>{t("cta.benefit_3")}</span>
							</div>
						</div>
					</CardBody>
				</Card>
			</div>
		</section>
	);
}
