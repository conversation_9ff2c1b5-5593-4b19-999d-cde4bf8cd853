"use client";

import { Chip } from "@heroui/chip";
import { <PERSON><PERSON>, Card, CardBody, Image } from "@heroui/react";
import NextImage from "next/image";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { subtitle, title } from "@/components/primitives";
import { featuredTemplates } from "@/lib/constants";

export default function TemplatesShowcase() {
	const t = useTranslations("homepage");

	return (
		<section
			className="py-20 md:py-32 bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20"
			id="templates"
		>
			<div className="max-w-6xl mx-auto px-4">
				<div className="text-center mb-16">
					<h2 className={title({ size: "md" })}>
						{t("templates.section_title")}
					</h2>
					<p className={subtitle({ class: "mt-4 max-w-2xl mx-auto" })}>
						{t("templates.section_subtitle")}
					</p>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
					{featuredTemplates.map((template) => (
						<Card
							key={template.id}
							className="border-none shadow-xl hover:shadow-2xl transition-shadow"
						>
							<CardBody className="p-0">
								<div className="relative rounded-t-lg overflow-hidden group">
									<Image
										isBlurred
										isZoomed
										alt={`${template.name} resume template preview`}
										as={NextImage}
										className="object-cover"
										height={500}
										radius="none"
										src={`/assets/images/templates/${template.id}.jpg`}
										width={400}
									/>
								</div>
								<div className="p-6">
									<div className="flex items-center justify-between mb-3">
										<h3 className="text-xl font-semibold">{template.name}</h3>
										<Chip color="success" size="sm" variant="flat">
											ATS Optimized
										</Chip>
									</div>
									<p className="text-default-600 mb-4">
										{t(`templates.descriptions.${template.id}`)}
									</p>
									<div className="flex items-center justify-between">
										<Button
											as={Link}
											color="primary"
											href={`/templates/${template.id}`}
											size="sm"
											variant="flat"
										>
											{t("templates.preview")}
										</Button>
									</div>
								</div>
							</CardBody>
						</Card>
					))}
				</div>

				<div className="text-center">
					<Button
						as={Link}
						className="font-semibold"
						color="primary"
						href="/templates"
						size="lg"
						variant="solid"
					>
						{t("templates.view_all")}
					</Button>
				</div>
			</div>
		</section>
	);
}
