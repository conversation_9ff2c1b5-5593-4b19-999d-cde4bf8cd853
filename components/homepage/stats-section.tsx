import { Card, CardBody } from "@heroui/card";
import { getTranslations } from "next-intl/server";
import { subtitle, title } from "@/components/primitives";

const stats = [
	{
		key: "resumes_created",
		value: "50,000+",
		labelKey: "stats.resumes_created",
	},
	{
		key: "job_success_rate",
		value: "94%",
		labelKey: "stats.job_success_rate",
	},
	{
		key: "templates_available",
		value: "12",
		labelKey: "stats.templates_available",
	},
	{
		key: "languages_supported",
		value: "2",
		labelKey: "stats.languages_supported",
	},
];

export default async function StatsSection() {
	const t = await getTranslations("homepage");

	return (
		<section className="py-20 md:py-32 bg-gradient-to-br from-default-50 to-default-100 dark:from-default-900/20 dark:to-default-800/20">
			<div className="max-w-6xl mx-auto px-4">
				<div className="text-center mb-16">
					<h2 className={title({ size: "md" })}>{t("stats.section_title")}</h2>
					<p className={subtitle({ class: "mt-4 max-w-2xl mx-auto" })}>
						{t("stats.section_subtitle")}
					</p>
				</div>

				<div className="grid grid-cols-2 md:grid-cols-4 gap-6">
					{stats.map((stat) => (
						<Card key={stat.key} className="border-none shadow-lg">
							<CardBody className="p-6 text-center">
								<div className="text-3xl md:text-4xl font-bold text-primary mb-2">
									{stat.value}
								</div>
								<p className="text-sm md:text-base text-default-600">
									{t(stat.labelKey)}
								</p>
							</CardBody>
						</Card>
					))}
				</div>
			</div>
		</section>
	);
}
