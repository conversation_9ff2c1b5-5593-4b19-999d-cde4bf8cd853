import { Card, CardBody } from "@heroui/card";
import { getTranslations } from "next-intl/server";
import { subtitle, title } from "@/components/primitives";
import { features } from "@/lib/constants";

export default async function FeaturesSection() {
	const t = await getTranslations("homepage");

	return (
		<section className="py-20 md:py-32">
			<div className="max-w-6xl mx-auto px-4">
				<div className="text-center mb-16">
					<h2 className={title({ size: "md" })}>
						{t("features.section_title")}
					</h2>
					<p className={subtitle({ class: "mt-4 max-w-2xl mx-auto" })}>
						{t("features.section_subtitle")}
					</p>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
					{features.map((feature) => (
						<Card key={feature.key} className="border-none shadow-lg">
							<CardBody className="p-6 text-start">
								<div className="text-4xl mb-4">{feature.icon}</div>
								<h3 className="text-xl font-semibold mb-2">
									{t(feature.titleKey)}
								</h3>
								<p className="text-default-600">{t(feature.descriptionKey)}</p>
							</CardBody>
						</Card>
					))}
				</div>
			</div>
		</section>
	);
}
