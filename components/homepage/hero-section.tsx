"use client";
import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { useTranslations } from "next-intl";
import { subtitle, title } from "@/components/primitives";
import CreateResumeButton from "@/components/resume/createResumeButton";

export default function HeroSection() {
	const t = useTranslations("homepage");

	return (
		<section className="flex flex-col items-center justify-center gap-8 py-20 md:py-32">
			<div className="text-center max-w-4xl">
				<Chip className="mb-6" color="primary" variant="flat">
					✨ {t("hero.badge")}
				</Chip>

				<h1 className={title({ size: "lg" })}>
					{t("hero.title_part1")}{" "}
					<span className={title({ color: "violet", size: "lg" })}>
						{t("hero.title_highlight")}
					</span>{" "}
					{t("hero.title_part2")}
				</h1>

				<p className={subtitle({ class: "mt-6 max-w-2xl mx-auto" })}>
					{t("hero.subtitle")}
				</p>

				<div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
					<CreateResumeButton size="lg" />
					<Button
						as="a"
						className="font-semibold"
						href="#templates"
						size="lg"
						variant="bordered"
					>
						{t("hero.view_templates")}
					</Button>
				</div>

				<div className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-4 text-sm text-default-600">
					<div className="flex items-center gap-2">
						<span>✓</span>
						<span>{t("hero.feature_1")}</span>
					</div>
					<div className="flex items-center gap-2">
						<span>✓</span>
						<span>{t("hero.feature_2")}</span>
					</div>
					<div className="flex items-center gap-2">
						<span>✓</span>
						<span>{t("hero.feature_3")}</span>
					</div>
				</div>
			</div>
		</section>
	);
}
