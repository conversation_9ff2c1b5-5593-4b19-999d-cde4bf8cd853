import { Avatar } from "@heroui/avatar";
import { Card, CardBody } from "@heroui/card";
import { useTranslations } from "next-intl";
import { subtitle, title } from "@/components/primitives";
import { testimonials } from "@/lib/constants";

export default function TestimonialsSection() {
	const t = useTranslations("homepage");

	return (
		<section className="py-20 md:py-32">
			<div className="max-w-6xl mx-auto px-4">
				<div className="text-center mb-16">
					<h2 className={title({ size: "md" })}>
						{t("testimonials.section_title")}
					</h2>
					<p className={subtitle({ class: "mt-4 max-w-2xl mx-auto" })}>
						{t("testimonials.section_subtitle")}
					</p>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-3 gap-8">
					{testimonials.map((testimonial, index) => (
						<Card key={index} className="border-none shadow-lg">
							<CardBody className="p-6">
								<div className="flex mb-4">
									{[...Array(testimonial.rating)].map((_, i) => (
										<span key={i} className="text-yellow-400">
											★
										</span>
									))}
								</div>
								<p className="text-default-700 mb-6 italic">
									&ldquo;{testimonial.content}&rdquo;
								</p>
								<div className="flex items-center gap-4">
									<Avatar
										className="bg-gradient-to-br from-primary-500 to-secondary-500"
										name={testimonial.name}
										size="md"
									/>
									<div>
										<p className="font-semibold">{testimonial.name}</p>
										<p className="text-sm text-default-600">
											{testimonial.role} at {testimonial.company}
										</p>
									</div>
								</div>
							</CardBody>
						</Card>
					))}
				</div>
			</div>
		</section>
	);
}
