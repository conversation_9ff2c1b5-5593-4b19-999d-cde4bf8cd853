"use client";

import { useTranslations } from "next-intl";
import React, { useState } from "react";
import { Template<PERSON>enderer } from "@/components/resume/templates/template-registry";
import { ColorSchemeSelector } from "@/components/shared/ColorSchemeSelector";
import { TemplateSelector } from "@/components/shared/TemplateSelector";
import { ColorSchemeId } from "@/config/color-schemes";
import { Template } from "@/db/schema";
import { FullResume } from "@/db/schema";

interface TemplateGalleryProps {
	templates: Template[];
	sampleResume: FullResume;
	onTemplateSelect?: (template: Template) => void;
}

export const TemplateGallery: React.FC<TemplateGalleryProps> = ({
	templates,
	sampleResume,
	onTemplateSelect,
}) => {
	const [selectedTemplate, setSelectedTemplate] = useState<Template>(
		templates[0] || null,
	);
	const [selectedColorScheme, setSelectedColorScheme] =
		useState<ColorSchemeId>("blue");
	const t = useTranslations();
	const handleTemplateSelect = (templateId: number) => {
		const template = templates.find((t) => t.id === templateId);
		if (template) {
			setSelectedTemplate(template);
			onTemplateSelect?.(template);
		}
	};

	// Create modified resume with selected template and color scheme
	const previewResume: FullResume = {
		...sampleResume,
		template_id: selectedTemplate?.id || 1,
		colorScheme: selectedColorScheme,
	};

	return (
		<div className="grid grid-cols-1 lg:grid-cols-6 gap-6 w-full ">
			{/* Template Gallery */}
			<div className="lg:col-span-2 w-full lg:max-w-[450px] space-y-6 h-full max-h-screen overflow-y-auto">
				<div>
					<h1 className="text-3xl font-bold mb-4">
						{t("templates.page_title")}
					</h1>
					<p className="text-gray-600 dark:text-gray-400 mb-6">
						{t("templates.page_description")}
					</p>
				</div>
				{/* Color Scheme Selector */}
				<ColorSchemeSelector
					selectedColorScheme={selectedColorScheme}
					onColorSchemeChange={setSelectedColorScheme}
				/>
				{/* Template List */}
				<TemplateSelector
					className="w-full p-2"
					selectedTemplateId={selectedTemplate?.id || 0}
					templates={templates}
					onTemplateSelect={handleTemplateSelect}
				/>
			</div>

			{/* Template Preview */}
			<div className="lg:col-span-4 max-h-screen overflow-y-auto">
				<div className="sticky top-4">
					<div className="preview-container bg-gray-100 dark:bg-gray-800 p-6 rounded-lg">
						<div className="bg-white shadow-lg mx-auto overflow-hidden">
							{selectedTemplate ? (
								<TemplateRenderer
									className="w-full h-full"
									resume={previewResume}
									templates={templates}
								/>
							) : (
								<div className="flex items-center justify-center h-full text-gray-500">
									{t("templates.select_template_to_preview")}
								</div>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};
