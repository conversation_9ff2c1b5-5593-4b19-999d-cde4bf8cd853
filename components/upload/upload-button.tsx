"use client";

import { Button, Progress } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import { useFileUpload } from "@/hooks/use-file-upload";

interface UploadButtonProps {
	onUploadComplete?: (res: { url: string }[]) => void;
	onUploadError?: (error: Error) => void;
	endpoint: "imageUploader" | "resumePhotoUploader";
	className?: string;
	disabled?: boolean;
	size?: "sm" | "md" | "lg";
	children?: React.ReactNode;
}

export function UploadButton({
	onUploadComplete,
	onUploadError,
	endpoint,
	className,
	disabled,
	size = "md",
	children,
}: UploadButtonProps) {
	const t = useTranslations("validation");

	const { uploadSingleFile, isUploading, uploadProgress } = useFileUpload({
		endpoint,
		onSuccess: onUploadComplete,
		onError: onUploadError,
	});

	const handleFileSelect = async (
		event: React.ChangeEvent<HTMLInputElement>,
	) => {
		const file = event.target.files?.[0];
		if (!file) return;

		await uploadSingleFile(file);
	};

	return (
		<div className="flex flex-col gap-2">
			<Button
				as="label"
				className={className}
				color="primary"
				disabled={disabled || isUploading}
				isLoading={isUploading}
				size={size}
				startContent={!isUploading && <Icon icon="lucide:upload" />}
			>
				{children || t("upload_file")}
				<input
					accept="image/*"
					className="hidden"
					disabled={disabled || isUploading}
					type="file"
					onChange={handleFileSelect}
				/>
			</Button>
			{isUploading && (
				<Progress
					aria-label="Upload progress"
					color="primary"
					size="sm"
					value={uploadProgress}
					showValueLabel={true}
					className="w-full"
				/>
			)}
		</div>
	);
}
