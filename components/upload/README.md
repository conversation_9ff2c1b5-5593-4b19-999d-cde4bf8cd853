# UploadThing File Upload Components

This directory contains reusable file upload components powered by UploadThing for the QuickCV application.

## Components

### `UploadButton`
A simple button-style file upload component.

```tsx
<UploadButton
  endpoint="imageUploader"
  onUploadComplete={(res) => console.log("Files:", res)}
  onUploadError={(error) => console.error(error)}
/>
```

### `UserPhotoUploadThing`
Resume photo upload component with preview and delete functionality.

```tsx
<UserPhotoUploadThing
  photo={resume.photo}
  resumeId={resume.id}
  onPhotoUpdate={(photoUrl) => updatePhoto(photoUrl)}
/>
```

## Hooks

### `useFileUpload`
Custom hook for programmatic file uploads.

```tsx
const { uploadSingleFile, isUploading, uploadProgress } = useFileUpload({
  endpoint: "imageUploader",
  onSuccess: (res) => console.log("Upload complete:", res),
  onError: (error) => console.error("Upload failed:", error)
});
```

## Configuration

### Environment Variables
Add to your `.env.local`:

```env
UPLOADTHING_SECRET=your_uploadthing_secret_key
UPLOADTHING_APP_ID=your_uploadthing_app_id
```

### API Routes
- `/api/uploadthing` - Main UploadThing endpoint
- `/api/resumes/[id]` - Resume update endpoint for photo management

### Upload Endpoints
- `imageUploader` - General image uploads (4MB max)
- `resumePhotoUploader` - Resume photo uploads (4MB max)

## Features

- ✅ File type validation (images only)
- ✅ File size limits (4MB)
- ✅ Upload progress tracking
- ✅ Authentication integration (Clerk)
- ✅ Error handling with toast notifications
- ✅ Automatic database integration for resume photos
- ✅ Photo deletion functionality
- ✅ Responsive design
- ✅ Dark mode support

## Setup

1. Sign up at [uploadthing.com](https://uploadthing.com)
2. Get your API keys and add them to `.env.local`
3. Import and use the components in your forms
4. The components are already integrated into the resume photo upload flow

## Integration

The upload components are integrated into:
- **Personal Information Form**: Resume photo upload
- **Resume API**: Photo URL updates in database
- **Authentication**: User-specific file uploads

All uploaded files are automatically associated with the authenticated user and stored securely via UploadThing's CDN.