"use client";

import { SignedIn, SignedOut, SignInButton, UserButton } from "@clerk/nextjs";
import {
	Navbar as HeroUINavbar,
	NavbarBrand,
	NavbarContent,
	NavbarItem,
	NavbarMenu,
	NavbarMenuItem,
	NavbarMenuToggle,
} from "@heroui/navbar";
import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import NextLink from "next/link";
import { useTranslations } from "next-intl";
import { Logo } from "@/components/icons";
import { ThemeSwitch } from "@/components/theme-switch";
import { LanguageSwitcher } from "./language-switcher";
import CreateResumeButton from "./resume/createResumeButton";

export const Navbar = () => {
	const t = useTranslations("navigation");
	const tResumes = useTranslations("resumes");

	return (
		<HeroUINavbar maxWidth="xl" position="sticky">
			<NavbarContent className="basis-1/5 sm:basis-full" justify="start">
				<NavbarBrand as="li" className="gap-3 max-w-fit">
					<NextLink className="flex justify-start items-center gap-1" href="/">
						<Logo />
						<p className="font-bold text-inherit">QuickCV</p>
					</NextLink>
				</NavbarBrand>
				<ul className="hidden lg:flex gap-4 justify-start items-center ms-2">
					<NavbarItem>
						<Link color="foreground" href="/about">
							{t("about")}
						</Link>
					</NavbarItem>
					<NavbarItem>
						<Link color="foreground" href="/templates">
							{t("templates")}
						</Link>
					</NavbarItem>
					<SignedIn>
						<NavbarItem>
							<Link color="foreground" href="/resumes">
								{t("resumes")}
							</Link>
						</NavbarItem>
						<NavbarItem>
							<Link color="foreground" href="/websites">
								{t("websites")}
							</Link>
						</NavbarItem>
						<CreateResumeButton size="sm" text={tResumes("create_resume")} />
					</SignedIn>
				</ul>
			</NavbarContent>

			<NavbarContent
				className="hidden sm:flex basis-1/5 sm:basis-full"
				justify="end"
			>
				<NavbarItem className="hidden sm:flex gap-2">
					<ThemeSwitch />
					<LanguageSwitcher />
				</NavbarItem>

				<NavbarItem className="hidden md:flex">
					<SignedOut>
						<SignInButton mode="modal">
							<Button color="primary" variant="flat">
								{t("signin")}
							</Button>
						</SignInButton>
					</SignedOut>
					<SignedIn>
						<UserButton afterSignOutUrl="/" />
					</SignedIn>
				</NavbarItem>
			</NavbarContent>

			<NavbarContent className="sm:hidden basis-1 pl-4" justify="end">
				<SignedOut>
					<SignInButton mode="modal">
						<Button color="primary" size="sm" variant="flat">
							{t("signin")}
						</Button>
					</SignInButton>
				</SignedOut>
				<SignedIn>
					<UserButton afterSignOutUrl="/" />
				</SignedIn>
				<NavbarMenuToggle />
			</NavbarContent>

			<NavbarMenu>
				<div className="flex flex-col items-start justify-center w-full">
					<>
						<div className="ms-4">
							<ThemeSwitch />
						</div>
						<LanguageSwitcher />
					</>
				</div>
				<div className="mx-4 mt-2 flex flex-col gap-4">
					<NavbarMenuItem>
						<Link color="foreground" href="/about">
							{t("about")}
						</Link>
					</NavbarMenuItem>

					<NavbarMenuItem>
						<Link color="foreground" href="/templates">
							{t("templates")}
						</Link>
					</NavbarMenuItem>
					<SignedIn>
						<NavbarMenuItem>
							<Link color="foreground" href="/resumes">
								{t("resumes")}
							</Link>
						</NavbarMenuItem>
						<NavbarMenuItem>
							<Link color="foreground" href="/websites">
								{t("websites")}
							</Link>
						</NavbarMenuItem>
						<CreateResumeButton text={tResumes("create_resume")} />
					</SignedIn>
				</div>
			</NavbarMenu>
		</HeroUINavbar>
	);
};
