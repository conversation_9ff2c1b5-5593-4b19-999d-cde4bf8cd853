"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import React from "react";
import { Template } from "@/db/schema";

interface TemplateSelectorProps {
	templates: Template[];
	selectedTemplateId?: number;
	onTemplateSelect: (templateId: number) => void;
	className?: string;
	maxHeight?: string;
	gridCols?: number;
	showDetails?: boolean;
	showCategory?: boolean;
	showFeatures?: boolean;
	imageHeight?: string;
}

export const TemplateSelector: React.FC<TemplateSelectorProps> = ({
	templates,
	selectedTemplateId,
	onTemplateSelect,
	className = "",
}) => {
	const t = useTranslations();
	const renderTemplateCard = (template: Template) => {
		const isSelected = selectedTemplateId === template.id;

		return (
			<Card
				key={template.id}
				isPressable
				className={`cursor-pointer transition-all hover:shadow-lg ${
					isSelected
						? "ring-2 ring-primary border-primary/50"
						: "hover:border-gray-300"
				}`}
				onPress={() => onTemplateSelect(template.id)}
			>
				<CardBody className={"p-3 "}>
					<div className={`flex flex-col gap-3`}>
						{/* Template Thumbnail */}
						<div className={`relative w-full min-h-44 rounded bg-gray-100`}>
							<Image
								width={640}
								height={905}
								alt={template.name}
								className="object-cover"
								src={`/assets/images/templates/${template.slug}.jpg`}
							/>
							{isSelected && (
								<div className="absolute top-1 right-1 w-5 h-5 bg-primary rounded-full flex items-center justify-center">
									<Icon className="text-white w-3 h-3" icon="lucide:check" />
								</div>
							)}
						</div>

						{/* Template Info */}
						<div className="flex-1 min-w-0">
							<h3 className={`font-semibold text-foreground truncate text-sm`}>
								{template.name}
							</h3>

							{template.category && (
								<Chip className={"mt-1 text-xs"} size="sm" variant="flat">
									{template.category}
								</Chip>
							)}
						</div>
					</div>
				</CardBody>
			</Card>
		);
	};

	const content = (
		<div className={`grid grid-cols-2 gap-3 p-2 ${className}`}>
			{templates.map(renderTemplateCard)}
		</div>
	);

	return (
		<Card className="shadow-sm">
			<CardHeader className="pb-3">
				<div className="flex items-center gap-2">
					<Icon
						className="text-blue-600 dark:text-blue-400"
						icon="lucide:layout-template"
					/>
					<h3 className="font-semibold text-gray-900 dark:text-gray-100">
						{t("navigation.templates")}
					</h3>
				</div>
			</CardHeader>
			<CardBody className="pt-0">{content}</CardBody>
		</Card>
	);
};

export default TemplateSelector;
