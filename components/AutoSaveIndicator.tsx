"use client";

import { Chip, CircularProgress } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";

interface AutoSaveIndicatorProps {
	isSaving: boolean;
	lastSaved?: Date;
	className?: string;
}

export const AutoSaveIndicator: React.FC<AutoSaveIndicatorProps> = ({
	isSaving,
	lastSaved,
	className = "",
}) => {
	const [showSuccess, setShowSuccess] = useState(false);
	const t = useTranslations();

	// Show success indicator briefly after saving
	useEffect(() => {
		if (!isSaving && lastSaved) {
			setShowSuccess(true);
			const timer = setTimeout(() => {
				setShowSuccess(false);
			}, 2000); // Show success for 2 seconds

			return () => clearTimeout(timer);
		}
	}, [isSaving, lastSaved]);

	if (isSaving) {
		return (
			<Chip
				size="sm"
				color="primary"
				variant="flat"
				className={className}
				startContent={
					<CircularProgress
						size="sm"
						color="primary"
						isIndeterminate={true}
						aria-label="Saving..."
						classNames={{
							svg: "w-3 h-3",
						}}
					/>
				}
			>
				{t("common.saving")}
			</Chip>
		);
	}

	if (showSuccess && lastSaved) {
		return (
			<Chip
				size="sm"
				color="success"
				variant="flat"
				className={className}
				startContent={<Icon icon="lucide:check" className="w-3 h-3" />}
			>
				{t("validation.saved")}
			</Chip>
		);
	}

	if (lastSaved && !showSuccess) {
		const timeAgo = getTimeAgo(lastSaved);
		return (
			<Chip
				size="sm"
				color="default"
				variant="light"
				className={`${className} text-xs text-gray-500`}
				startContent={<Icon icon="lucide:clock" className="w-3 h-3" />}
			>
				{t("validation.last_saved", { time: timeAgo })}
			</Chip>
		);
	}

	return null;
};

function getTimeAgo(date: Date): string {
	const now = new Date();
	const diffMs = now.getTime() - date.getTime();
	const diffMinutes = Math.floor(diffMs / (1000 * 60));

	if (diffMinutes < 1) {
		return "just now";
	} else if (diffMinutes === 1) {
		return "1 minute ago";
	} else if (diffMinutes < 60) {
		return `${diffMinutes} minutes ago`;
	} else {
		const diffHours = Math.floor(diffMinutes / 60);
		if (diffHours === 1) {
			return "1 hour ago";
		} else {
			return `${diffHours} hours ago`;
		}
	}
}
