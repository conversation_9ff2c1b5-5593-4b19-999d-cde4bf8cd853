"use client";

import {
	closestCenter,
	DndContext,
	type DragEndEvent,
	KeyboardSensor,
	PointerSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	SortableContext,
	sortableKeyboardCoordinates,
	useSortable,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslations } from "next-intl";
import { ReactNode, useCallback, useState } from "react";
import { deleteNestedItem } from "@/actions/resumes";
import { upperCaseFirstLetter } from "@/lib/utils";

export interface AccordionFormListProps<
	TItem extends { id: string | number; sort: number },
> {
	items: TItem[];
	titlePrefix: string;
	headerTitle?: string;
	renderForm: (item: TItem, index: number) => ReactNode;
	className?: string;
	keyName?: string;
	baseRoute: (id: number) => string;
	resumeId: number;
}

interface DragHandleProps {
	attributes: Omit<
		React.HTMLAttributes<HTMLDivElement>,
		"role" | "tabIndex" | "onKeyDown" | "onMouseDown" | "onTouchStart"
	> & {
		role?: string;
		tabIndex?: number;
		"aria-label"?: string;
		"aria-describedby"?: string;
	};
	listeners: {
		onKeyDown?: (event: React.KeyboardEvent) => void;
		onMouseDown?: (event: React.MouseEvent) => void;
		onTouchStart?: (event: React.TouchEvent) => void;
	};
}

function DragHandle({ attributes, listeners }: DragHandleProps) {
	const { onMouseDown, onTouchStart, onKeyDown, ...restListeners } = listeners;
	const { role = "button", tabIndex = 0, ...restAttributes } = attributes;

	const handleMouseDown = useCallback(
		(e: React.MouseEvent) => {
			e.stopPropagation();
			e.preventDefault();
			onMouseDown?.(e);
		},
		[onMouseDown],
	);

	const handleTouchStart = useCallback(
		(e: React.TouchEvent) => {
			e.stopPropagation();
			e.preventDefault();
			onTouchStart?.(e);
		},
		[onTouchStart],
	);

	const handleKeyDown = useCallback(
		(e: React.KeyboardEvent) => {
			if (e.key === "Enter" || e.key === " ") {
				e.stopPropagation();
				e.preventDefault();
			}
			onKeyDown?.(e);
		},
		[onKeyDown],
	);

	return (
		<div
			aria-describedby="drag-handle-description"
			aria-label="Drag to reorder"
			className="cursor-move p-1 hover:bg-default-200 rounded transition-colors"
			role={role}
			tabIndex={tabIndex}
			onKeyDown={handleKeyDown}
			onMouseDown={handleMouseDown}
			onTouchStart={handleTouchStart}
			{...restAttributes}
			{...restListeners}
		>
			<Icon className="text-default-400" icon="lucide:grip-vertical" />
		</div>
	);
}

interface SortableItemProps {
	id: string;
	children: (props: { attributes: any; listeners: any }) => React.ReactNode;
}

function SortableItem({ id, children }: SortableItemProps) {
	const { attributes, listeners, setNodeRef, transform, transition } =
		useSortable({ id });

	return (
		<div
			ref={setNodeRef}
			style={{
				transform: CSS.Transform.toString(transform),
				transition,
			}}
		>
			{children({ attributes, listeners })}
		</div>
	);
}

export default function AccordionFormList<
	TItem extends { id: string | number; sort: number },
>({
	items: initialItems,
	titlePrefix,
	baseRoute,
	resumeId,
	keyName,
	renderForm,
	className = "",
	headerTitle,
}: AccordionFormListProps<TItem>) {
	const [expandedItems, setExpandedItems] = useState<Set<string | number>>(
		new Set(),
	);
	const [items, setItems] = useState<TItem[]>(initialItems);
	const t = useTranslations();
	const sensors = useSensors(
		useSensor(PointerSensor),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		}),
	);

	const getName = useCallback(
		(item: TItem, index: number) => {
			return item[keyName as keyof TItem]
				? String(item[keyName as keyof TItem])
				: t("forms.item_number", { prefix: titlePrefix, number: index + 1 });
		},
		[keyName, titlePrefix, t],
	);

	const handleDragEnd = useCallback(
		(event: DragEndEvent) => {
			const { active, over } = event;
			if (over && active.id !== over.id) {
				const oldIndex = items.findIndex(
					(item) => String(item.id) === active.id,
				);
				const newIndex = items.findIndex((item) => String(item.id) === over.id);

				if (oldIndex !== -1 && newIndex !== -1) {
					const newItems = [...items];
					const [movedItem] = newItems.splice(oldIndex, 1);
					newItems.splice(newIndex, 0, movedItem);

					// Update sort order to match new positions
					const updatedItems = newItems.map((item, index) => ({
						...item,
						sort: index + 1, // 1-based index for sort order
					}));

					// Update local state with new sort order
					setItems(updatedItems);
				}
			}
		},
		[items],
	);

	const toggleExpanded = useCallback((itemId: string | number) => {
		setExpandedItems((prev) => {
			const newSet = new Set(prev);
			if (newSet.has(itemId)) {
				newSet.delete(itemId);
			} else {
				newSet.add(itemId);
			}
			return newSet;
		});
	}, []);

	const deleteItem = useCallback(
		(item: TItem) => {
			if (window.confirm(t("validation.confirm_delete"))) {
				const newItems = items.filter((i) => i.id !== item.id);
				setItems(newItems);
				deleteNestedItem(baseRoute(resumeId), item.id, resumeId);
			}
		},
		[items, t, baseRoute, resumeId],
	);

	return (
		<div className={className}>
			{headerTitle && (
				<h2 className="text-xl font-semibold mb-4">{headerTitle}</h2>
			)}
			{items?.length === 0 ? (
				<p className="text-center">{t("validation.no_items_found")}</p>
			) : (
				<DndContext
					collisionDetection={closestCenter}
					sensors={sensors}
					onDragEnd={handleDragEnd}
				>
					<SortableContext
						items={items?.map((item) => item.id.toString())}
						strategy={verticalListSortingStrategy}
					>
						<div className="space-y-2">
							{items?.map((item, index) => (
								<SortableItem key={item.id.toString()} id={item.id.toString()}>
									{(dragHandleProps) => (
										<div className="border rounded-lg border-default-200">
											<div
												aria-expanded={expandedItems.has(item.id)}
												className="flex items-center justify-between p-3 bg-default-50 rounded-t-lg cursor-pointer hover:bg-default-100 transition-colors"
												role="button"
												tabIndex={0}
												onClick={() => toggleExpanded(item.id)}
												onKeyDown={(e) => {
													if (e.key === "Enter" || e.key === " ") {
														e.preventDefault();
														toggleExpanded(item.id);
													}
												}}
											>
												<div className="flex items-center gap-2">
													<DragHandle {...dragHandleProps} />
													<Icon
														className="text-default-400 transition-transform duration-200"
														icon={
															expandedItems.has(item.id)
																? "lucide:chevron-down"
																: "lucide:chevron-right"
														}
													/>
													<span className="text-base font-medium">
														{upperCaseFirstLetter(getName(item, index))}
													</span>
												</div>
												<Button
													isIconOnly
													color="danger"
													size="sm"
													variant="light"
													onPress={() => deleteItem(item)}
												>
													<Icon icon="lucide:trash-2" />
												</Button>
											</div>
											{expandedItems.has(item.id) && (
												<div className="p-4 border-t border-default-200">
													{renderForm(item, index)}
												</div>
											)}
										</div>
									)}
								</SortableItem>
							))}
						</div>
					</SortableContext>
				</DndContext>
			)}
		</div>
	);
}
