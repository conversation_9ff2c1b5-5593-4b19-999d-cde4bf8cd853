import { useRouter } from "@bprogress/next/app";
import { useActionState } from "react";
import toast from "react-hot-toast";

type UseFormActionOptions<T> = {
	onSuccess?: (result: T) => void;
	onError?: (result: T) => void;
	successMessage?: string;
	errorMessage?: string;
	redirectTo?: string;
};

export function useFormAction<T, D extends FormData | any>(
	actionFn: (data: D) => Promise<T>,
	initialState: Awaited<T>,
	options?: UseFormActionOptions<Awaited<T>>,
) {
	const router = useRouter();

	const [state, formAction, isPending] = useActionState<T, D>(
		async (_prevState: T, data: D) => {
			try {
				const result = await actionFn(data);
				if ((result as any).success) {
					toast.success(options?.successMessage || "Success");
					if (options?.redirectTo) router.push(options.redirectTo);
					options?.onSuccess?.(result);
				} else {
					toast.error(
						(result as any).error ||
							(result as any).message ||
							options?.errorMessage ||
							"Something went wrong",
					);
					options?.onError?.(result);
				}

				return result;
			} catch (err) {
				toast.error(options?.errorMessage || "An error occurred");
				return {
					...(initialState as any),
					success: false,
					message: (err as Error).message || "An error occurred",
				};
			}
		},
		initialState,
	);

	return { state, formAction, isPending };
}
