export const routes = {
	educations_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/educations`,
	experiences_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/experiences`,
	projects_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/projects`,
	awards_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/awards`,
	certifications_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/certifications`,
	skills_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/skills`,
	languages_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/languages`,
	references_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/references`,
	hobbies_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/hobbies`,
	volunteerings_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/volunteerings`,
	add_education_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/educations`,
	add_experience_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/experiences`,
	add_project_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/projects`,
	add_award_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/awards`,
	add_certification_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/certifications`,
	add_skill_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/skills`,
	add_language_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/languages`,
	add_reference_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/references`,
	add_hobby_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/hobbies`,
	add_volunteering_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/volunteerings`,
	resumeEditPath: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/edit/${id}`,
	resumesPath: (locale: string = "ar") => `/${locale}/resumes`,
	resumePath: (id: number, locale: string = "ar") => `/${locale}/resumes/${id}`,
	fetchResumePath: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}`,
	fetchResumesPath: (locale: string = "ar") => `/${locale}/resumes`,
	templatesPath: (locale: string = "ar") => `/${locale}/templates`,
	profiles_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/profiles`,
	add_profile_url: (id: number, locale: string = "ar") =>
		`/${locale}/resumes/${id}/profiles`,
};
