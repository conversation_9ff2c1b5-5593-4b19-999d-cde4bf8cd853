// Color schemes for resume customization
export const COLOR_SCHEMES = {
	blue: {
		id: "blue",
		name: "Professional Blue",
		primary: "#3B82F6",
		text: "#1F2937",
		background: "#FFFFFF",
	},
	green: {
		id: "green",
		name: "<PERSON> Green",
		primary: "#10B981",
		text: "#1F2937",
		background: "#FFFFFF",
	},
	purple: {
		id: "purple",
		name: "Creative Purple",
		primary: "#8B5CF6",
		text: "#1F2937",
		background: "#FFFFFF",
	},
	red: {
		id: "red",
		name: "Bold Red",
		primary: "#EF4444",
		text: "#1F2937",
		background: "#FFFFFF",
	},
	orange: {
		id: "orange",
		name: "Energetic Orange",
		primary: "#F97316",
		text: "#1F2937",
		background: "#FFFFFF",
	},
	gray: {
		id: "gray",
		name: "Classic Gray",
		primary: "#6B7280",
		text: "#1F2937",
		background: "#FFFFFF",
	},
};

export type ColorSchemeId = keyof typeof COLOR_SCHEMES;
export type ColorScheme = (typeof COLOR_SCHEMES)[ColorSchemeId];
