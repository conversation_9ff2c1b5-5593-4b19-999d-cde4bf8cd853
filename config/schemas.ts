export const referenceSchema = {
	type: "object",
	collection: "references",
	description: "Add your references",
	entity: "reference",
	properties: {
		name: { type: "string", placeholder: "Full name" },
		company: { type: "string", placeholder: "Company" },
		position: { type: "string", placeholder: "Position" },
		email: { type: "string", placeholder: "Email address" },
		phone: { type: "string", placeholder: "Phone number" },
		description: { type: "textarea", label: "Description" },
	},
	required: ["name", "email"],
};

export const projectSchema = {
	type: "object",
	collection: "projects",
	description: "Add your projects",
	entity: "project",
	properties: {
		title: {
			type: "string",
			placeholder: "Project title",
		},
		client: { type: "string", placeholder: "Client" },
		start_date: { type: "date", placeholder: "From" },
		end_date: { type: "date", placeholder: "To" },
		url: {
			type: ["string", "null"],
			placeholder: "https://www.example.com",
		},
		description: { type: "textarea", label: "Description" },
	},
	required: ["title"],
};

export const languageSchema = {
	type: "object",
	collection: "languages",
	description: "Add your languages",
	entity: "language",
	properties: {
		name: { type: "string", placeholder: "Language name" },
		proficiency: {
			type: "number",
			placeholder: "Proficiency level (1-100)",
		},
	},
	required: ["name"],
};

export const hobbySchema = {
	type: "object",
	collection: "hobbies",
	description: "Add your hobbies",
	entity: "hobby",
	properties: {
		name: {
			type: "string",
			placeholder: "Hobby name",
			className: "col-span-full",
		},
	},
	required: ["name"],
};

export const experienceSchema = {
	type: "object",
	collection: "experiences",
	description: "Add your experiences",
	entity: "experience",
	properties: {
		company: { type: "string", label: "Company Name" },
		title: { type: "string", label: "Job Title" },
		city: { type: "string", label: "City" },
		country: { type: "string", label: "Country" },
		start_date: { type: "date", label: "From" },
		end_date: { type: "date", label: "To" },
		is_current: { type: "boolean", label: "I am currently employed here" },
		description: { type: "textarea", label: "Summary" },
	},
};

export const educationSchema = {
	type: "object",
	collection: "educations",
	description: "Add your educational background",
	entity: "education",
	properties: {
		institution: {
			type: "string",
			label: "Institution name",
		},
		field_of_study: {
			type: "string",
			label: "Field of Study",
		},
		degree: {
			type: "string",
			label: "Degree",
		},
		city: {
			type: "string",
			label: "City",
		},
		country: {
			type: "string",
			label: "Country",
		},
		start_date: {
			type: "date",
			label: "From",
		},
		end_date: {
			type: "date",
			label: "To",
		},
		website: {
			type: "string",
			placeholder: "https://www.example.com",
			label: "Website",
		},
		is_current: {
			type: "boolean",
			label: "I currently study here",
		},
		description: {
			type: "textarea",
			label: "Summary",
		},
	},
};

export const certificationSchema = {
	type: "object",
	collection: "certifications",
	description: "Add your certifications",
	entity: "certification",
	properties: {
		title: { type: "string", placeholder: "Certification name" },
		issuer: { type: "string", placeholder: "Issuer" },
		url: {
			type: ["string", "null"],
			placeholder: "https://www.example.com",
		},
		date_received: {
			type: "date",
			placeholder: "Date of certification",
		},
		description: { type: "textarea", label: "Description" },
	},
	required: ["title", "issuer"],
};

export const awardSchema = {
	type: "object",
	collection: "awards",
	description: "Add your awards",
	entity: "award",
	properties: {
		title: { type: "string", placeholder: "Award title" },
		issuer: { type: "string", placeholder: "Issuer" },
		date_received: {
			type: "date",
			placeholder: "Date received",
		},
		url: {
			type: ["string", "null"],
			placeholder: "https://www.example.com",
		},
		description: { type: "textarea", label: "Description" },
	},
	required: ["title", "issuer"],
};

export const volunteeringSchema = {
	type: "object",
	collection: "volunteerings",
	description: "Add your volunteerings",
	entity: "volunteering",
	properties: {
		organization: { type: "string", placeholder: "Organization" },
		role: { type: "string", placeholder: "Role" },
		start_date: { type: "date", placeholder: "From" },
		end_date: { type: "date", placeholder: "To" },
		description: { type: "textarea", label: "Description" },
	},
	required: ["organization", "role"],
};

export const skillSchema = {
	type: "object",
	collection: "skills",
	description: "Add your skills",
	entity: "skill",
	properties: {
		name: { type: "string", placeholder: "Skill name" },
		category: { type: "string", placeholder: "Category" },
		proficiency: {
			type: "number",
			placeholder: "Proficiency level (1-100)",
		},
	},
	required: ["name"],
};

export const profilesSchema = {
	type: "object",
	collection: "profiles",
	description: "Add your profiles",
	entity: "profile",
	properties: {
		username: { type: "string", placeholder: "john.doe", label: "Username" },
		url: {
			type: "string",
			placeholder: "https://github.com/johndoe",
			label: "URL",
		},
		network: { type: "string", placeholder: "Github", label: "Network" },
		icon: {
			type: "string",
			placeholder: "github",
			label: "Icon",
			description: "Powered by Simple Icons",
		},
	},
};
