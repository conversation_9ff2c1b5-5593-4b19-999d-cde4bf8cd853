{"navigation": {"home": "Home", "resumes": "Resumes", "websites": "Websites", "templates": "Templates", "about": "About", "logout": "Logout", "profile": "Profile", "settings": "Settings", "sponsor": "Sponsor", "pricing": "Pricing", "signin": "Sign In"}, "common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No", "or": "or", "and": "and", "required": "Required", "optional": "Optional", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "saving": "Saving...", "saved": "Saved", "save_error": "Save failed", "unsaved_changes": "Unsaved changes", "all_saved": "All changes saved", "just_now": "Just now", "minutes_ago": "{count} minutes ago", "auto_save": "Auto-save"}, "resumes": {"page_title": "My Resumes", "page_subtitle": "You have {count} resumes", "no_resumes_title": "No resumes created yet", "no_resumes_description": "Start building your professional resume using our advanced templates and easy-to-use tools.", "search_placeholder": "Search resumes...", "filter": "Filter", "grid_view": "Grid View", "list_view": "List View", "total_resumes": "Total Resumes", "updated_this_week": "Updated This Week", "templates_used": "Templates Used", "error_title": "Error loading resumes", "error_description": "Please try again later or contact support.", "create_resume": "Create Resume", "get_started": "Get started", "add_new_resume": "Add New Resume", "title": "Title", "enter_resume_title": "Enter Resume title", "create": "Create", "untitled_resume": "untitled resume", "today": "Today", "yesterday": "Yesterday", "days_ago": "{count}d ago", "complete": "Complete", "no_results_title": "No resumes found", "no_results_description": "Try adjusting your search terms or clear the search to see all resumes.", "actions": {"edit": "Edit", "website": "Website", "download": "Download", "more_options": "More options", "create_website": "Create Website", "duplicate": "Duplicate", "share": "Share", "export": "Export", "delete": "Delete"}}, "templates": {"page_title": "Resume Templates", "page_description": "Choose from a wide range of professional templates designed to help you land your dream job.", "select": "Select", "selectTemplate": "Select Template", "preview": "Preview", "useTemplate": "Use This Template", "free": "Free", "premium": "Premium", "ats_friendly": "ATS Friendly", "modern": "Modern", "classic": "Classic", "creative": "Creative", "professional": "Professional", "minimalist": "Minimalist", "colorful": "Colorful", "no_templates": "No templates available", "ats_score": "ATS Score", "excellent": "Excellent", "good": "Good", "fair": "Fair", "ats_description": "ATS (Applicant Tracking System) compatibility measures how easily your resume can be read by automated hiring systems."}, "forms": {"item_number": "{prefix} #{number}", "personal_information": "Personal Information", "showPhoto": "Show photo", "firstName": "First name", "lastName": "Last name", "jobTitle": "Job title", "email": "Email", "phone": "Phone", "birthDate": "Birth date", "website": "Website", "linkedin": "LinkedIn", "github": "GitHub", "twitter": "Twitter", "city": "City", "street": "Street", "country": "Country", "address": "Address", "bio": "Bio", "summary": "Summary", "experience": "Experience", "education": "Education", "skills": "Skills", "languages": "Languages", "projects": "Projects", "certifications": "Certifications", "profiles": "Profiles", "references": "References", "volunteer_experience": "Volunteer Experience", "volunteering": "Volunteering", "awards": "Awards & Achievements", "interests": "Interests & Hobbies", "hobbies": "Hobbies", "available_upon_request": "Available upon request", "add_new": "Add New", "full_name": "Full name", "company": "Company", "position": "Position", "start_date": "Start date", "end_date": "End date", "startDate": "Start Date", "endDate": "End Date", "current": "Current", "description": "Description", "institution": "Institution", "degree": "Degree", "field_of_study": "Field of study", "fieldOfStudy": "Field of Study", "gpa": "GPA", "skill_name": "Skill name", "skillName": "Skill Name", "level": "Level", "language_name": "Language name", "languageName": "Language", "proficiency": "Proficiency", "fluency": "Fluency", "project_name": "Project name", "projectName": "Project Name", "projectUrl": "Project URL", "technologies": "Technologies", "url": "URL", "certification_name": "Certification name", "certificateName": "Certificate Name", "issuing_organization": "Issuing organization", "issuingOrganization": "Issuing Organization", "issue_date": "Issue date", "issueDate": "Issue Date", "expirationDate": "Expiration Date", "credentialId": "Credential ID", "profile_name": "Profile name", "profileUrl": "Profile URL", "profileType": "Profile Type", "username": "Username", "network": "Network", "icon": "Icon", "category": "Category", "reference_name": "Reference name", "referenceName": "Reference Name", "referenceTitle": "Reference Title", "referenceCompany": "Reference Company", "referenceEmail": "Reference Email", "referencePhone": "Reference Phone", "relationship": "Relationship", "award_name": "Award name", "awardName": "Award Name", "awardingOrganization": "Awarding Organization", "awardDate": "Award Date", "date": "Date", "hobby_name": "Hobby name", "hobbyName": "Hobby Name", "organizationName": "Organization Name", "reference": "References", "project": "Projects", "language": "Languages", "hobby": "Hobbies", "certification": "Certifications", "award": "Awards", "skill": "Skills", "profile": "Social Profiles", "role": "Role", "cause": "Cause", "singular": {"reference": "Reference", "project": "Project", "language": "Language", "hobby": "<PERSON>bby", "certification": "Certification", "award": "Award", "skill": "Skill", "profile": "Profile", "experience": "Experience", "education": "Education", "volunteering": "Volunteering"}, "sections": {"references": "Add your references", "projects": "Add your projects", "languages": "Add your languages", "hobbies": "Add your hobbies", "experiences": "Add your experiences", "educations": "Add your educational background", "certifications": "Add your certifications", "awards": "Add your awards", "volunteerings": "Add your volunteerings", "skills": "Add your skills", "profiles": "Add your profiles"}, "placeholders": {"email_address": "Email address", "phone_number": "Phone number", "project_title": "Project title", "client": "Client", "from": "From", "to": "To", "url_example": "https://www.example.com", "proficiency_level": "Proficiency level (1-100)", "company_name": "Company Name", "institution_name": "Institution name", "certification_name": "Certification name", "issuer": "Issuer", "date_of_certification": "Date of certification", "award_title": "Award title", "date_received": "Date received", "organization": "Organization", "username": "john.doe", "github_url": "https://github.com/johndoe", "github": "<PERSON><PERSON><PERSON>", "github_icon": "github"}, "labels": {"currently_employed": "I am currently employed here", "currently_study": "I currently study here", "icon_description": "Powered by Simple Icons"}}, "settings": {"customize_resume": "Customize Resume", "customize_description": "Choose templates, colors, fonts...", "colors": "Colors", "choose_color_scheme": "Choose a color scheme", "primary_color": "Primary Color", "primaryColor": "Primary Color", "secondary_color": "Secondary Color", "secondaryColor": "Secondary Color", "background_color": "Background Color", "backgroundColor": "Background Color", "text_color": "Text Color", "textColor": "Text Color", "fonts": "Fonts", "font_family": "Font Family", "fontFamily": "Font Family", "font_size": "Font Size", "fontSize": "Font Size", "spacing": "Spacing", "layout": "Layout", "margin": "<PERSON><PERSON>", "margins": "<PERSON><PERSON>", "padding": "Padding", "line_height": "Line Height", "lineHeight": "Line Height"}, "preview": {"customize_your_resume": "Customize Your Resume", "customize_description": "Change templates, colors, fonts...", "customize": "Customize", "download_pdf": "Download PDF", "preview": "Preview", "print": "Print", "generating_thumbnail": "Generating thumbnail...", "exporting": "Exporting..."}, "validation": {"file_too_large": "File is too large. Max size is 4MB", "upload_photo": "Upload photo", "delete_photo": "Delete photo", "required_field": "This field is required", "invalid_email": "Invalid email address", "invalid_url": "Invalid URL", "confirm_delete": "Are you sure you want to delete this item?", "no_items_found": "No items found. Add one to get started.", "drag_to_reorder": "Drag to reorder", "upload_success": "Upload successful", "upload_error": "Upload failed", "uploading": "Uploading...", "upload_file": "Upload file", "drag_drop_files": "Drag and drop files here", "or_click_to_browse": "Or click to browse", "photo_deleted": "Photo deleted successfully", "delete_failed": "Failed to delete photo", "update_failed": "Failed to update resume", "last_saved": "Last saved {time}"}, "homepage": {"edit_instruction": "Get started by editing app/page.tsx", "github": "GitHub", "hero": {"badge": "Free Resume Builder", "title_part1": "Create Your Perfect", "title_highlight": "Professional Resume", "title_part2": "in Minutes", "subtitle": "Build ATS-optimized resumes with professional templates. Choose from 12 beautiful designs, edit in real-time, and download instantly.", "view_templates": "View Templates", "feature_1": "ATS Optimized", "feature_2": "12 Templates", "feature_3": "Free Forever"}, "features": {"section_title": "Why Choose QuickCV?", "section_subtitle": "Everything you need to create a resume that gets noticed by employers and passes through ATS systems.", "templates": {"title": "Professional Templates", "description": "Choose from 12 beautiful, ATS-optimized templates designed by experts."}, "ats": {"title": "ATS Optimized", "description": "Our templates are designed to pass through Applicant Tracking Systems."}, "instant": {"title": "Instant Editing", "description": "Real-time editing with auto-save. See changes instantly as you type."}, "multilingual": {"title": "Multi-language Support", "description": "Create resumes in English or Arabic with RTL layout support."}, "autosave": {"title": "Auto-save", "description": "Never lose your work. Your resume is automatically saved as you edit."}, "website": {"title": "Website Publishing", "description": "Transform your resume into a professional website with a custom URL."}, "responsive": {"title": "Mobile Friendly", "description": "Edit your resume on any device. Works perfectly on mobile and desktop."}}, "templates": {"section_title": "Professional Resume Templates", "section_subtitle": "Choose from our collection of ATS-optimized templates designed to help you stand out.", "preview": "Preview", "view_all": "View All Templates", "descriptions": {"azurill": "Clean and modern design perfect for tech professionals", "chikorita": "Professional layout ideal for corporate environments", "bronzor": "Minimalist design that highlights your experience"}}, "stats": {"section_title": "Trusted by Job Seekers Worldwide", "section_subtitle": "Join thousands of professionals who have successfully landed their dream jobs using QuickCV.", "resumes_created": "Resumes Created", "job_success_rate": "Job Success Rate", "templates_available": "Templates Available", "languages_supported": "Languages Supported"}, "testimonials": {"section_title": "What Our Users Say", "section_subtitle": "Real feedback from professionals who landed their dream jobs using QuickCV."}, "cta": {"title": "Ready to Build Your Perfect Resume?", "subtitle": "Join thousands of professionals who have successfully landed their dream jobs using QuickCV.", "explore_templates": "Explore Templates", "benefit_1": "Free Forever", "benefit_2": "No Credit Card Required", "benefit_3": "Download Instantly"}, "seo": {"title": "QuickCV - Free Professional Resume Builder | ATS Optimized Templates", "description": "Create professional resumes in minutes with our free resume builder. Choose from 12 ATS-optimized templates, edit in real-time, and download instantly. Perfect for job seekers worldwide.", "keywords": "resume builder, CV maker, professional resume, ATS optimized, free resume templates, job application, career tools, resume creator, online resume builder, professional CV"}}, "auth": {"login": "<PERSON><PERSON>", "signup": "Sign Up", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "signInHere": "Sign in here", "signUpHere": "Sign up here"}, "errors": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "url": "Please enter a valid URL", "date": "Please enter a valid date", "minLength": "Text must be at least {min} characters", "maxLength": "Text must not exceed {max} characters", "generic": "An unexpected error occurred", "networkError": "Network connection error", "unauthorized": "You are not authorized to access this", "forbidden": "Access forbidden", "notFound": "Page not found", "serverError": "Server error"}, "success": {"saved": "Saved successfully", "created": "Created successfully", "updated": "Updated successfully", "deleted": "Deleted successfully", "uploaded": "Uploaded successfully", "downloaded": "Downloaded successfully", "shared": "Shared successfully", "copied": "Copied to clipboard"}, "datePicker": {"selectDate": "Select Date", "today": "Today", "clear": "Clear", "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "days": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}}, "proficiency": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "expert": "Expert", "native": "Native"}, "websites": {"page_title": "My Websites", "page_subtitle": "You have {count} websites", "no_websites_title": "No websites yet", "no_websites_subtitle": "Create your first website from one of your resumes", "create_first_website": "Create Website", "published": "Published", "draft": "Draft", "template": "Template", "website_url": "Website URL", "publish": "Publish", "unpublish": "Unpublish", "confirm_delete": "Are you sure you want to delete this website?"}, "website": {"builder": {"title": "Website Builder", "description": "Create a beautiful public website from your resume", "choose_template": "<PERSON><PERSON>", "website_settings": "Website Settings", "website_status": "Website Status", "template": "Template", "preview": "Preview"}, "background": {"title": "<PERSON> Pattern", "description": "Choose a subtle background pattern to enhance your website's visual appeal", "categories": {"all": "All", "recommended": "Recommended", "minimal": "Minimal", "textured": "Textured", "gradient": "Gradient"}, "recommended": "Recommended", "no_patterns": "No patterns available for this filter", "show_all": "Show All", "reset": "Reset to Clean", "use_recommended": "Use Recommended"}, "status": {"published": "Published", "draft": "Draft", "public": "Public", "private": "Private", "live_at": "Your website is live at:"}, "settings": {"website_url": "Website URL", "website_url_description": "This will be your public website URL", "custom_title": "Custom Title (Optional)", "custom_title_description": "Override the job title from your resume", "custom_bio": "Custom Bio (Optional)", "custom_bio_description": "Override the bio from your resume", "show_contact": "Show contact information", "checking_availability": "Checking availability...", "url_placeholder": "your-name-developer"}, "actions": {"create_website": "Create Website", "update_website": "Update Website", "publish_website": "Publish Website", "unpublish_website": "Unpublish Website"}, "templates": {"portfolio": {"name": "Portfolio", "description": "Modern single-page layout with hero section, experience timeline, and skills grid", "features": ["Hero section", "Timeline layout", "Skills grid", "Project showcase"], "best_for": ["Developers", "Designers", "Creative professionals"]}, "professional": {"name": "Professional", "description": "Traditional CV layout with clean two-column design and professional styling", "features": ["Two-column layout", "Professional header", "Sidebar sections", "Clean typography"], "best_for": ["Corporate professionals", "Consultants", "Traditional industries"]}}, "validation": {"slug_required": "URL slug is required", "slug_min_length": "URL slug must be at least 3 characters long", "slug_format": "URL slug can only contain lowercase letters, numbers, and hyphens", "slug_taken": "This URL is already taken. Please choose a different one.", "slug_error": "Error checking URL availability"}, "messages": {"created_success": "Website created successfully!", "updated_success": "Website updated successfully!", "published_success": "Website published successfully!", "unpublished_success": "Website unpublished", "create_failed": "Failed to create website", "update_failed": "Failed to update website", "publish_failed": "Failed to publish website", "unexpected_error": "An unexpected error occurred"}, "sections": {"about": "About", "experience": "Experience", "projects": "Projects", "skills": "Skills", "education": "Education", "contact": "Contact", "get_in_touch": "Get In Touch"}, "footer": {"created_with": "Created with", "copyright": "© {year} {name}"}}, "site": {"name": "QuickCV", "description": "Create your perfect resume in minutes", "tagline": "Your perfect resume starts here"}}