import { createId } from "@paralleldrive/cuid2";
import { sql } from "drizzle-orm";
import {
	integer,
	primaryKey,
	real,
	sqliteTable,
	text,
} from "drizzle-orm/sqlite-core";

// Users table
export const users = sqliteTable("users", {
	id: text("id")
		.primaryKey()
		.$defaultFn(() => createId()),
	clerkId: text("clerk_id").notNull().unique(),
	emailAddress: text("email_address").notNull().unique(),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Templates table
export const templates = sqliteTable("templates", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	name: text("name").notNull(),
	slug: text("slug").notNull().unique(),
	atsScore: text("ats_score"),
	category: text("category"),
	features: text("features"),
	description: text("description"),
	image: text("image"),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Resumes table
export const resumes = sqliteTable("resumes", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	title: text("title"),
	firstName: text("first_name").notNull().default(""),
	lastName: text("last_name").notNull().default(""),
	jobTitle: text("job_title").notNull().default(""),
	address: text("address").notNull().default(""),
	phone: text("phone").notNull().default(""),
	email: text("email").notNull().default(""),
	website: text("website").notNull().default(""),
	bio: text("bio").notNull().default(""),
	birthDate: text("birth_date").notNull().default(""),
	city: text("city").notNull().default(""),
	street: text("street").notNull().default(""),
	country: text("country").notNull().default(""),
	showPhoto: integer("showPhoto", { mode: "boolean" }).notNull().default(false),
	colorScheme: text("color_scheme").default("blue").notNull(),
	fontFamily: text("font_family").default("inter").notNull(),
	spacing: text("spacing").default("normal").notNull(),
	margins: text("margins").default("normal").notNull(),
	photo: text("photo").notNull().default(""),
	thumbnail: text("thumbnail").notNull().default(""),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	userId: text("user_id").notNull(),
	templateId: integer("template_id").default(1).notNull(),
});

// Education table
export const educations = sqliteTable("educations", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	city: text("city"),
	country: text("country"),
	institution: text("institution"),
	isCurrent: integer("is_current", { mode: "boolean" }).default(false),
	startDate: text("start_date").notNull().default(""),
	endDate: text("end_date").notNull().default(""),
	description: text("description"),
	degree: text("degree"),
	fieldOfStudy: text("field_of_study"),
	website: text("website"),
	sort: integer("sort").default(0),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	resumeId: integer("resume_id").notNull(),
});

// Experience table
export const experiences = sqliteTable("experiences", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	title: text("title"),
	company: text("company"),
	startDate: text("start_date").notNull().default(""),
	endDate: text("end_date").notNull().default(""),
	description: text("description"),
	city: text("city"),
	country: text("country"),
	isCurrent: integer("is_current", { mode: "boolean" })
		.default(false)
		.notNull(),
	sort: integer("sort").default(0),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	resumeId: integer("resume_id").notNull(),
});

// Projects table
export const projects = sqliteTable("projects", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	title: text("title"),
	client: text("client"),
	url: text("url"),
	startDate: text("start_date").notNull().default(""),
	endDate: text("end_date").notNull().default(""),
	description: text("description"),
	sort: integer("sort").default(0),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	resumeId: integer("resume_id").notNull(),
});

// Awards table
export const awards = sqliteTable("awards", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	title: text("title"),
	issuer: text("issuer"),
	url: text("url"),
	dateReceived: text("date_received"),
	description: text("description"),
	sort: integer("sort").default(0),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	resumeId: integer("resume_id").notNull(),
});

// Certifications table
export const certifications = sqliteTable("certifications", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	title: text("title"),
	issuer: text("issuer"),
	url: text("url"),
	dateReceived: text("date_received"),
	description: text("description"),
	sort: integer("sort").default(0),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	resumeId: integer("resume_id").notNull(),
});

// Skills table
export const skills = sqliteTable("skills", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	name: text("name"),
	proficiency: integer("proficiency"),
	category: text("category"),
	sort: integer("sort").default(0),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	resumeId: integer("resume_id").notNull(),
});

// Languages table
export const languages = sqliteTable("languages", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	name: text("name"),
	proficiency: integer("proficiency").notNull().default(10),
	sort: integer("sort").default(0),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	resumeId: integer("resume_id").notNull(),
});

// References table
export const references = sqliteTable("references", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	name: text("name"),
	company: text("company"),
	position: text("position"),
	email: text("email"),
	phone: text("phone"),
	description: text("description"),
	sort: integer("sort").default(0),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	resumeId: integer("resume_id").notNull(),
});

// Hobbies table
export const hobbies = sqliteTable("hobbies", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	name: text("name"),
	description: text("description"),
	sort: integer("sort").default(0),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	resumeId: integer("resume_id").notNull(),
});

// Volunteering table
export const volunteerings = sqliteTable("volunteerings", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	organization: text("organization"),
	role: text("role"),
	startDate: text("start_date"),
	endDate: text("end_date"),
	description: text("description"),
	sort: integer("sort").default(0),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	resumeId: integer("resume_id").notNull(),
});

// Profiles table
export const profiles = sqliteTable("profiles", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	url: text("url"),
	username: text("username"),
	network: text("network").notNull().default(""),
	icon: text("icon"),
	sort: integer("sort").default(0),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	resumeId: integer("resume_id").notNull(),
});

// Website Templates table
export const websiteTemplates = sqliteTable("website_templates", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	name: text("name").notNull(),
	slug: text("slug").notNull().unique(),
	preview: text("preview").notNull(),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Websites table
export const websites = sqliteTable("websites", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	slug: text("slug").notNull().unique(),
	isPublic: integer("is_public", { mode: "boolean" }).default(false).notNull(),
	analytics: integer("analytics", { mode: "boolean" }).default(false).notNull(),
	backgroundPattern: text("background_pattern").default("none").notNull(),
	createdAt: text("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: text("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
	userId: text("user_id").notNull(),
	resumeId: integer("resume_id").notNull(),
	websiteTemplateId: integer("website_template_id").notNull(),
});

// Export all table types for use in application code
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

export type Template = typeof templates.$inferSelect;
export type NewTemplate = typeof templates.$inferInsert;

export type Resume = typeof resumes.$inferSelect;
export type NewResume = typeof resumes.$inferInsert;

export type Education = typeof educations.$inferSelect;
export type NewEducation = typeof educations.$inferInsert;

export type Experience = typeof experiences.$inferSelect;
export type NewExperience = typeof experiences.$inferInsert;

export type Project = typeof projects.$inferSelect;
export type NewProject = typeof projects.$inferInsert;

export type Award = typeof awards.$inferSelect;
export type NewAward = typeof awards.$inferInsert;

export type Certification = typeof certifications.$inferSelect;
export type NewCertification = typeof certifications.$inferInsert;

export type Skill = typeof skills.$inferSelect;
export type NewSkill = typeof skills.$inferInsert;

export type Language = typeof languages.$inferSelect;
export type NewLanguage = typeof languages.$inferInsert;

export type Reference = typeof references.$inferSelect;
export type NewReference = typeof references.$inferInsert;

export type Hobby = typeof hobbies.$inferSelect;
export type NewHobby = typeof hobbies.$inferInsert;

export type Volunteering = typeof volunteerings.$inferSelect;
export type NewVolunteering = typeof volunteerings.$inferInsert;

export type Profile = typeof profiles.$inferSelect;
export type NewProfile = typeof profiles.$inferInsert;

export type WebsiteTemplate = typeof websiteTemplates.$inferSelect;
export type NewWebsiteTemplate = typeof websiteTemplates.$inferInsert;

export type Website = typeof websites.$inferSelect;
export type NewWebsite = typeof websites.$inferInsert;

// Resume with all nested collections - matching the frontend expectations
export interface FullResume extends Resume {
	template?: Template | null;
	template_name?: string;
	template_id?: number;
	colorScheme: string;
	fontFamily: string;
	// All nested collections
	educations: Education[];
	experiences: Experience[];
	projects: Project[];
	awards: Award[];
	certifications: Certification[];
	skills: Skill[];
	languages: Language[];
	references: Reference[];
	hobbies: Hobby[];
	volunteerings: Volunteering[];
	profiles: Profile[];
}
