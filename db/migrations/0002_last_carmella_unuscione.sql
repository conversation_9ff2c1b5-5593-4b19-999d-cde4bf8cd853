PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_educations` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`city` text,
	`country` text,
	`institution` text,
	`is_current` integer DEFAULT false,
	`start_date` text DEFAULT '' NOT NULL,
	`end_date` text DEFAULT '' NOT NULL,
	`description` text,
	`degree` text,
	`field_of_study` text,
	`website` text,
	`sort` integer DEFAULT 0,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`resume_id` integer NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_educations`("id", "city", "country", "institution", "is_current", "start_date", "end_date", "description", "degree", "field_of_study", "website", "sort", "created_at", "updated_at", "resume_id") SELECT "id", "city", "country", "institution", "is_current", "start_date", "end_date", "description", "degree", "field_of_study", "website", "sort", "created_at", "updated_at", "resume_id" FROM `educations`;--> statement-breakpoint
DROP TABLE `educations`;--> statement-breakpoint
ALTER TABLE `__new_educations` RENAME TO `educations`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE TABLE `__new_experiences` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`title` text,
	`company` text,
	`start_date` text DEFAULT '' NOT NULL,
	`end_date` text DEFAULT '' NOT NULL,
	`description` text,
	`city` text,
	`country` text,
	`is_current` integer DEFAULT false NOT NULL,
	`sort` integer DEFAULT 0,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`resume_id` integer NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_experiences`("id", "title", "company", "start_date", "end_date", "description", "city", "country", "is_current", "sort", "created_at", "updated_at", "resume_id") SELECT "id", "title", "company", "start_date", "end_date", "description", "city", "country", "is_current", "sort", "created_at", "updated_at", "resume_id" FROM `experiences`;--> statement-breakpoint
DROP TABLE `experiences`;--> statement-breakpoint
ALTER TABLE `__new_experiences` RENAME TO `experiences`;--> statement-breakpoint
CREATE TABLE `__new_languages` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`name` text,
	`proficiency` integer DEFAULT 10 NOT NULL,
	`sort` integer DEFAULT 0,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`resume_id` integer NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_languages`("id", "name", "proficiency", "sort", "created_at", "updated_at", "resume_id") SELECT "id", "name", "proficiency", "sort", "created_at", "updated_at", "resume_id" FROM `languages`;--> statement-breakpoint
DROP TABLE `languages`;--> statement-breakpoint
ALTER TABLE `__new_languages` RENAME TO `languages`;--> statement-breakpoint
CREATE TABLE `__new_profiles` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`url` text,
	`username` text,
	`network` text DEFAULT '' NOT NULL,
	`icon` text,
	`sort` integer DEFAULT 0,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`resume_id` integer NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_profiles`("id", "url", "username", "network", "icon", "sort", "created_at", "updated_at", "resume_id") SELECT "id", "url", "username", "network", "icon", "sort", "created_at", "updated_at", "resume_id" FROM `profiles`;--> statement-breakpoint
DROP TABLE `profiles`;--> statement-breakpoint
ALTER TABLE `__new_profiles` RENAME TO `profiles`;--> statement-breakpoint
CREATE TABLE `__new_projects` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`title` text,
	`client` text,
	`url` text,
	`start_date` text DEFAULT '' NOT NULL,
	`end_date` text DEFAULT '' NOT NULL,
	`description` text,
	`sort` integer DEFAULT 0,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`resume_id` integer NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_projects`("id", "title", "client", "url", "start_date", "end_date", "description", "sort", "created_at", "updated_at", "resume_id") SELECT "id", "title", "client", "url", "start_date", "end_date", "description", "sort", "created_at", "updated_at", "resume_id" FROM `projects`;--> statement-breakpoint
DROP TABLE `projects`;--> statement-breakpoint
ALTER TABLE `__new_projects` RENAME TO `projects`;--> statement-breakpoint
CREATE TABLE `__new_resumes` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`title` text,
	`first_name` text DEFAULT '' NOT NULL,
	`last_name` text DEFAULT '' NOT NULL,
	`job_title` text DEFAULT '' NOT NULL,
	`address` text DEFAULT '' NOT NULL,
	`phone` text DEFAULT '' NOT NULL,
	`email` text DEFAULT '' NOT NULL,
	`website` text DEFAULT '' NOT NULL,
	`bio` text DEFAULT '' NOT NULL,
	`birth_date` text DEFAULT '' NOT NULL,
	`city` text DEFAULT '' NOT NULL,
	`street` text DEFAULT '' NOT NULL,
	`country` text DEFAULT '' NOT NULL,
	`showPhoto` integer DEFAULT false NOT NULL,
	`color_scheme` text DEFAULT 'blue' NOT NULL,
	`font_family` text DEFAULT 'inter' NOT NULL,
	`spacing` text DEFAULT 'normal' NOT NULL,
	`margins` text DEFAULT 'normal' NOT NULL,
	`photo` text DEFAULT '' NOT NULL,
	`thumbnail` text DEFAULT '' NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`user_id` text NOT NULL,
	`template_id` integer DEFAULT 1 NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_resumes`("id", "title", "first_name", "last_name", "job_title", "address", "phone", "email", "website", "bio", "birth_date", "city", "street", "country", "showPhoto", "color_scheme", "font_family", "spacing", "margins", "photo", "thumbnail", "created_at", "updated_at", "user_id", "template_id") SELECT "id", "title", "first_name", "last_name", "job_title", "address", "phone", "email", "website", "bio", "birth_date", "city", "street", "country", "showPhoto", "color_scheme", "font_family", "spacing", "margins", "photo", "thumbnail", "created_at", "updated_at", "user_id", "template_id" FROM `resumes`;--> statement-breakpoint
DROP TABLE `resumes`;--> statement-breakpoint
ALTER TABLE `__new_resumes` RENAME TO `resumes`;