import { sqliteTable, AnySQLiteColumn, integer, text, uniqueIndex } from "drizzle-orm/sqlite-core"
  import { sql } from "drizzle-orm"

export const awards = sqliteTable("awards", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	title: text(),
	issuer: text(),
	url: text(),
	dateReceived: text("date_received"),
	description: text(),
	sort: integer().default(0),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	resumeId: integer("resume_id").notNull(),
});

export const certifications = sqliteTable("certifications", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	title: text(),
	issuer: text(),
	url: text(),
	dateReceived: text("date_received"),
	description: text(),
	sort: integer().default(0),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	resumeId: integer("resume_id").notNull(),
});

export const educations = sqliteTable("educations", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	city: text(),
	country: text(),
	institution: text(),
	isCurrent: integer("is_current").default(false),
	startDate: text("start_date"),
	endDate: text("end_date"),
	description: text(),
	degree: text(),
	fieldOfStudy: text("field_of_study"),
	website: text(),
	sort: integer().default(0),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	resumeId: integer("resume_id").notNull(),
});

export const experiences = sqliteTable("experiences", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	title: text(),
	company: text(),
	startDate: text("start_date"),
	endDate: text("end_date"),
	description: text(),
	city: text(),
	country: text(),
	isCurrent: integer("is_current").default(false).notNull(),
	sort: integer().default(0),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	resumeId: integer("resume_id").notNull(),
});

export const hobbies = sqliteTable("hobbies", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	name: text(),
	description: text(),
	sort: integer().default(0),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	resumeId: integer("resume_id").notNull(),
});

export const languages = sqliteTable("languages", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	name: text(),
	proficiency: integer(),
	sort: integer().default(0),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	resumeId: integer("resume_id").notNull(),
});

export const profiles = sqliteTable("profiles", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	url: text(),
	username: text(),
	network: text(),
	icon: text(),
	sort: integer().default(0),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	resumeId: integer("resume_id").notNull(),
});

export const projects = sqliteTable("projects", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	title: text(),
	client: text(),
	url: text(),
	startDate: text("start_date"),
	endDate: text("end_date"),
	description: text(),
	sort: integer().default(0),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	resumeId: integer("resume_id").notNull(),
});

export const references = sqliteTable("references", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	name: text(),
	company: text(),
	position: text(),
	email: text(),
	phone: text(),
	description: text(),
	sort: integer().default(0),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	resumeId: integer("resume_id").notNull(),
});

export const skills = sqliteTable("skills", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	name: text(),
	proficiency: integer(),
	category: text(),
	sort: integer().default(0),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	resumeId: integer("resume_id").notNull(),
});

export const templates = sqliteTable("templates", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	name: text().notNull(),
	slug: text().notNull(),
	atsScore: text("ats_score"),
	category: text(),
	features: text(),
	description: text(),
	image: text(),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
},
(table) => [
	uniqueIndex("templates_slug_unique").on(table.slug),
]);

export const users = sqliteTable("users", {
	id: text().primaryKey().notNull(),
	clerkId: text("clerk_id").notNull(),
	emailAddress: text("email_address").notNull(),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
},
(table) => [
	uniqueIndex("users_email_address_unique").on(table.emailAddress),
	uniqueIndex("users_clerk_id_unique").on(table.clerkId),
]);

export const volunteerings = sqliteTable("volunteerings", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	organization: text(),
	role: text(),
	startDate: text("start_date"),
	endDate: text("end_date"),
	description: text(),
	sort: integer().default(0),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	resumeId: integer("resume_id").notNull(),
});

export const websiteTemplates = sqliteTable("website_templates", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	name: text().notNull(),
	slug: text().notNull(),
	preview: text().notNull(),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
},
(table) => [
	uniqueIndex("website_templates_slug_unique").on(table.slug),
]);

export const websites = sqliteTable("websites", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	slug: text().notNull(),
	isPublic: integer("is_public").default(false).notNull(),
	analytics: integer().default(false).notNull(),
	backgroundPattern: text("background_pattern").default("none").notNull(),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	userId: text("user_id").notNull(),
	resumeId: integer("resume_id").notNull(),
	websiteTemplateId: integer("website_template_id").notNull(),
},
(table) => [
	uniqueIndex("websites_slug_unique").on(table.slug),
]);

export const resumes = sqliteTable("resumes", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	title: text(),
	firstName: text("first_name"),
	lastName: text("last_name"),
	jobTitle: text("job_title"),
	address: text(),
	email: text().default(""),
	website: text(),
	bio: text(),
	birthDate: text("birth_date"),
	city: text(),
	street: text(),
	country: text(),
	showPhoto: integer().default(false),
	colorScheme: text("color_scheme").default("blue").notNull(),
	fontFamily: text("font_family").default("inter").notNull(),
	spacing: text().default("normal").notNull(),
	margins: text().default("normal").notNull(),
	photo: text(),
	thumbnail: text(),
	createdAt: text("created_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	updatedAt: text("updated_at").default("sql`(CURRENT_TIMESTAMP)`").notNull(),
	userId: text("user_id").notNull(),
	templateId: integer("template_id").default(1).notNull(),
});

