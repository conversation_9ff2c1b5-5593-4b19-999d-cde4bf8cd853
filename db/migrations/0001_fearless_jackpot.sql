PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_resumes` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`title` text,
	`first_name` text,
	`last_name` text,
	`job_title` text,
	`address` text,
	`email` text DEFAULT '',
	`website` text,
	`bio` text,
	`birth_date` text,
	`city` text,
	`street` text,
	`country` text,
	`showPhoto` integer DEFAULT false,
	`color_scheme` text DEFAULT 'blue' NOT NULL,
	`font_family` text DEFAULT 'inter' NOT NULL,
	`spacing` text DEFAULT 'normal' NOT NULL,
	`margins` text DEFAULT 'normal' NOT NULL,
	`photo` text,
	`thumbnail` text,
	`created_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP NOT NULL,
	`user_id` text NOT NULL,
	`template_id` integer DEFAULT 1 NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_resumes`("id", "title", "first_name", "last_name", "job_title", "address", "email", "website", "bio", "birth_date", "city", "street", "country", "showPhoto", "color_scheme", "font_family", "spacing", "margins", "photo", "thumbnail", "created_at", "updated_at", "user_id", "template_id") SELECT "id", "title", "first_name", "last_name", "job_title", "address", "email", "website", "bio", "birth_date", "city", "street", "country", "showPhoto", "color_scheme", "font_family", "spacing", "margins", "photo", "thumbnail", "created_at", "updated_at", "user_id", "template_id" FROM `resumes`;--> statement-breakpoint
DROP TABLE `resumes`;--> statement-breakpoint
ALTER TABLE `__new_resumes` RENAME TO `resumes`;--> statement-breakpoint
PRAGMA foreign_keys=ON;